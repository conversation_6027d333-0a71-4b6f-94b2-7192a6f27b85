"""
LabVIEW集成接口
提供命令行接口和文件I/O接口，确保LabVIEW可以调用Python脚本
"""

import argparse
import json
import os
import sys
import logging
from pathlib import Path
from typing import Dict, Optional, Union, List, Tuple
import numpy as np
import open3d as o3d
import tempfile

# 导入项目模块
from image_processor import ImageProcessor
from geometry_builder import GeometryBuilder
from texture_mapper import TextureMapper
from viewer_3d import Interactive3DViewer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cube_3d_visualizer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class LabVIEWInterface:
    """LabVIEW接口类"""
    
    def __init__(self):
        """初始化LabVIEW接口"""
        self.image_processor = ImageProcessor()
        self.geometry_builder = GeometryBuilder()
        self.texture_mapper = TextureMapper()
        self.viewer = None
        
        # 临时文件目录
        self.temp_dir = tempfile.mkdtemp(prefix="cube3d_")
        logger.info(f"临时目录已创建: {self.temp_dir}")
    
    def process_images(self, image_paths: Dict[str, str], 
                      enhance: bool = False) -> Dict[str, np.ndarray]:
        """
        处理输入图片
        
        Args:
            image_paths: 图片路径字典
            enhance: 是否进行图像增强
            
        Returns:
            处理后的图片字典
        """
        return self.image_processor.process_cube_images(image_paths, enhance)
    
    def create_3d_model(self, processed_images: Dict[str, np.ndarray], 
                       dimensions: Tuple[float, float, float] = (2.0, 2.0, 2.0),
                       use_atlas: bool = True) -> o3d.geometry.TriangleMesh:
        """
        创建3D模型
        
        Args:
            processed_images: 处理后的图片字典
            dimensions: 长方体尺寸 (width, height, depth)
            use_atlas: 是否使用纹理图集
            
        Returns:
            带纹理的3D网格
        """
        # 创建几何体
        self.geometry_builder = GeometryBuilder(dimensions)
        mesh = self.geometry_builder.create_mesh()
        
        # 应用纹理
        textured_mesh = self.texture_mapper.apply_texture_to_mesh(
            mesh, processed_images, use_atlas)
        
        logger.info("3D模型创建完成")
        return textured_mesh
    
    def display_model(self, mesh: o3d.geometry.TriangleMesh, 
                     window_name: str = "3D长方体查看器") -> None:
        """
        显示3D模型
        
        Args:
            mesh: 3D网格
            window_name: 窗口名称
        """
        self.viewer = Interactive3DViewer(window_name=window_name)
        
        # 创建线框
        wireframe = self.geometry_builder.create_wireframe()
        
        # 添加到查看器
        self.viewer.add_cube_mesh(mesh, wireframe)
        
        # 运行查看器
        self.viewer.run()
    
    def save_model(self, mesh: o3d.geometry.TriangleMesh, 
                  output_path: str, 
                  format: str = "obj") -> bool:
        """
        保存3D模型
        
        Args:
            mesh: 3D网格
            output_path: 输出路径
            format: 输出格式 ("obj", "ply", "stl")
            
        Returns:
            是否成功保存
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 根据格式保存
            if format.lower() == "obj":
                o3d.io.write_triangle_mesh(output_path, mesh, write_triangle_uvs=True)
            elif format.lower() == "ply":
                o3d.io.write_triangle_mesh(output_path, mesh)
            elif format.lower() == "stl":
                o3d.io.write_triangle_mesh(output_path, mesh)
            else:
                logger.error(f"不支持的格式: {format}")
                return False
            
            logger.info(f"模型已保存: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            return False
    
    def create_json_output(self, mesh: o3d.geometry.TriangleMesh, 
                         processed_images: Dict[str, np.ndarray],
                         output_path: str) -> bool:
        """
        创建JSON输出（用于LabVIEW交互）
        
        Args:
            mesh: 3D网格
            processed_images: 处理后的图片
            output_path: 输出路径
            
        Returns:
            是否成功保存
        """
        try:
            # 提取网格信息
            vertices = np.asarray(mesh.vertices)
            triangles = np.asarray(mesh.triangles)
            
            # 创建输出字典
            output_data = {
                "mesh_info": {
                    "num_vertices": len(vertices),
                    "num_triangles": len(triangles),
                    "dimensions": {
                        "width": self.geometry_builder.width,
                        "height": self.geometry_builder.height,
                        "depth": self.geometry_builder.depth
                    }
                },
                "image_info": {
                    "num_images": len(processed_images),
                    "image_sizes": {
                        face: img.shape[:2] for face, img in processed_images.items()
                    }
                },
                "face_info": {}
            }
            
            # 添加面信息
            face_info = self.geometry_builder.get_face_info()
            for face_name, info in face_info.items():
                output_data["face_info"][face_name] = {
                    "center": info["center"].tolist(),
                    "normal": info["normal"].tolist()
                }
            
            # 保存JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"JSON输出已保存: {output_path}")
            return True
        except Exception as e:
            logger.error(f"创建JSON输出失败: {e}")
            return False
    
    def process_and_display(self, image_paths: Dict[str, str], 
                          dimensions: Tuple[float, float, float] = (2.0, 2.0, 2.0),
                          enhance: bool = False,
                          output_path: Optional[str] = None,
                          output_format: str = "obj",
                          display: bool = True) -> Optional[o3d.geometry.TriangleMesh]:
        """
        处理图片并显示/保存3D模型（主要接口）
        
        Args:
            image_paths: 图片路径字典
            dimensions: 长方体尺寸
            enhance: 是否进行图像增强
            output_path: 输出路径（可选）
            output_format: 输出格式
            display: 是否显示模型
            
        Returns:
            3D网格（如果成功）
        """
        try:
            # 处理图片
            processed_images = self.process_images(image_paths, enhance)
            if not processed_images:
                logger.error("图片处理失败")
                return None
            
            # 创建3D模型
            mesh = self.create_3d_model(processed_images, dimensions)
            
            # 保存模型（如果指定）
            if output_path:
                self.save_model(mesh, output_path, output_format)
                
                # 创建JSON输出
                json_path = output_path.replace(f".{output_format}", ".json")
                self.create_json_output(mesh, processed_images, json_path)
            
            # 显示模型（如果需要）
            if display:
                self.display_model(mesh)
            
            return mesh
        
        except Exception as e:
            logger.error(f"处理和显示失败: {e}")
            return None
    
    def cleanup(self) -> None:
        """清理临时文件"""
        import shutil
        try:
            shutil.rmtree(self.temp_dir)
            logger.info(f"临时目录已清理: {self.temp_dir}")
        except Exception as e:
            logger.error(f"清理临时目录失败: {e}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="3D长方体可视化工具")
    
    # 输入图片参数
    parser.add_argument("--front", required=True, help="前面图片路径")
    parser.add_argument("--back", required=True, help="后面图片路径")
    parser.add_argument("--left", required=True, help="左面图片路径")
    parser.add_argument("--right", required=True, help="右面图片路径")
    parser.add_argument("--top", required=True, help="上面图片路径")
    
    # 可选参数
    parser.add_argument("--width", type=float, default=2.0, help="长方体宽度")
    parser.add_argument("--height", type=float, default=2.0, help="长方体高度")
    parser.add_argument("--depth", type=float, default=2.0, help="长方体深度")
    parser.add_argument("--enhance", action="store_true", help="启用图像增强")
    parser.add_argument("--output", help="输出模型路径")
    parser.add_argument("--format", default="obj", choices=["obj", "ply", "stl"], help="输出格式")
    parser.add_argument("--no-display", action="store_true", help="不显示3D模型")
    
    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 创建图片路径字典
    image_paths = {
        "front": args.front,
        "back": args.back,
        "left": args.left,
        "right": args.right,
        "top": args.top
    }
    
    # 创建接口实例
    interface = LabVIEWInterface()
    
    try:
        # 处理和显示
        dimensions = (args.width, args.height, args.depth)
        mesh = interface.process_and_display(
            image_paths=image_paths,
            dimensions=dimensions,
            enhance=args.enhance,
            output_path=args.output,
            output_format=args.format,
            display=not args.no_display
        )
        
        # 检查结果
        if mesh is None:
            logger.error("处理失败")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"发生错误: {e}")
        sys.exit(1)
    finally:
        # 清理资源
        interface.cleanup()


if __name__ == "__main__":
    main()

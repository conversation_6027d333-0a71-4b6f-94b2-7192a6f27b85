# 3D图片拼接可视化系统

## 项目简介
这是一个将5张2D图片（前、后、左、右、上面）拼接成3D长方体模型的可视化系统。支持交互式3D查看和LabVIEW集成。

## 功能特性
- 📸 支持PNG/JPG等常见图片格式
- 🎯 自动纹理映射到3D长方体
- 🖱️ 交互式3D查看器（旋转、缩放、平移）
- 🔧 LabVIEW集成接口
- 💾 支持导出3D模型文件

## 技术栈
- **Python 3.8+**
- **OpenCV** - 图像处理
- **Open3D** - 3D可视化
- **NumPy** - 数值计算

## 安装依赖
```bash
pip install -r requirements.txt
```

## 快速开始
```python
from src.box_3d_visualizer import Box3DVisualizer

# 创建可视化器
visualizer = Box3DVisualizer()

# 加载图片并生成3D模型
model = visualizer.create_3d_model({
    'front': 'images/front.jpg',
    'back': 'images/back.jpg', 
    'left': 'images/left.jpg',
    'right': 'images/right.jpg',
    'top': 'images/top.jpg'
})

# 显示3D模型
visualizer.show_interactive(model)
```

## LabVIEW集成
```bash
# 命令行调用
python src/labview_interface.py --front front.jpg --back back.jpg --left left.jpg --right right.jpg --top top.jpg --output result.ply
```

## 项目结构
```
图片拼接成3d/
├── src/                    # 源代码
│   ├── __init__.py
│   ├── image_processor.py  # 图像处理模块
│   ├── geometry_builder.py # 3D几何建模
│   ├── texture_mapper.py   # 纹理映射
│   ├── viewer_3d.py        # 3D查看器
│   ├── box_3d_visualizer.py # 主要接口
│   └── labview_interface.py # LabVIEW接口
├── examples/               # 示例和测试
├── tests/                  # 单元测试
├── requirements.txt        # 依赖包
└── README.md              # 项目说明
```

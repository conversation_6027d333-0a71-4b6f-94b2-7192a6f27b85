# LabVIEW集成指南

## 方案1: System Exec调用 (推荐)

### 1.1 基本调用

在LabVIEW中使用System Exec VI调用Python脚本：

**VI结构:**
```
[图片路径控件] → [构建命令字符串] → [System Exec] → [结果显示]
```

**命令格式:**
```
python "D:\Git_Doc\图片拼接成3d\src\labview_interface.py" --front "front.jpg" --back "back.jpg" --left "left.jpg" --right "right.jpg" --top "top.jpg" --output "result.obj" --no-display
```

### 1.2 LabVIEW代码示例

**前面板控件:**
- String Control: `Python路径` (默认: python)
- String Control: `脚本路径` (默认: D:\Git_Doc\图片拼接成3d\src\labview_interface.py)
- Path Control: `前面图片`
- Path Control: `后面图片`
- Path Control: `左面图片`
- Path Control: `右面图片`
- Path Control: `上面图片`
- Path Control: `输出路径`
- Numeric Control: `宽度` (默认: 2.0)
- Numeric Control: `高度` (默认: 2.0)
- Numeric Control: `深度` (默认: 2.0)
- Boolean Control: `图像增强`
- String Indicator: `执行结果`
- Boolean Indicator: `执行状态`

**程序框图逻辑:**
1. 构建命令字符串
2. 调用System Exec
3. 解析返回结果
4. 检查输出文件是否生成

### 1.3 错误处理

**检查项目:**
- Python环境是否正确
- 图片文件是否存在
- 输出目录是否可写
- 脚本执行返回码

## 方案2: JSON文件通信

### 2.1 工作流程

```
LabVIEW → 写入配置JSON → Python处理 → 写入结果JSON → LabVIEW读取结果
```

### 2.2 配置文件格式

**输入配置 (config.json):**
```json
{
  "input_images": {
    "front": "C:/images/front.jpg",
    "back": "C:/images/back.jpg",
    "left": "C:/images/left.jpg",
    "right": "C:/images/right.jpg",
    "top": "C:/images/top.jpg"
  },
  "dimensions": {
    "width": 2.0,
    "height": 2.0,
    "depth": 2.0
  },
  "processing_options": {
    "enhance": true,
    "use_atlas": false
  },
  "output_options": {
    "model_path": "C:/output/model.obj",
    "format": "obj",
    "display": false
  }
}
```

**结果文件 (result.json):**
```json
{
  "success": true,
  "output_files": {
    "model": "C:/output/model.obj",
    "info": "C:/output/model.json"
  },
  "model_info": {
    "num_vertices": 8,
    "num_triangles": 12,
    "dimensions": {
      "width": 2.0,
      "height": 2.0,
      "depth": 2.0
    }
  },
  "processing_time": 2.5,
  "error_message": null
}
```

### 2.3 LabVIEW实现步骤

1. **写入配置文件**
   - 使用JSON VI写入配置
   - 确保路径使用正斜杠或双反斜杠

2. **调用Python脚本**
   ```
   python json_processor.py config.json
   ```

3. **读取结果文件**
   - 轮询检查结果文件
   - 解析JSON获取处理结果

## 方案3: Python Node调用

### 3.1 环境配置

**步骤:**
1. 在LabVIEW中配置Python环境路径
2. 添加模块搜索路径
3. 导入Python模块

**Python环境设置:**
- Python解释器路径: `D:\python_home\python.exe`
- 模块路径: `D:\Git_Doc\图片拼接成3d\src`

### 3.2 Python Node代码

```python
import sys
sys.path.append(r'D:\Git_Doc\图片拼接成3d\src')

from image_processor import ImageProcessor
from geometry_builder_simple import SimpleGeometryBuilder

def process_cube_images(front_path, back_path, left_path, right_path, top_path, 
                       output_path, width=2.0, height=2.0, depth=2.0, enhance=False):
    try:
        # 图片路径字典
        image_paths = {
            'front': front_path,
            'back': back_path,
            'left': left_path,
            'right': right_path,
            'top': top_path
        }
        
        # 处理图片
        processor = ImageProcessor()
        processed_images = processor.process_cube_images(image_paths, enhance)
        
        if not processed_images:
            return False, "图片处理失败"
        
        # 创建几何体
        builder = SimpleGeometryBuilder(dimensions=(width, height, depth))
        vertices = builder.create_box_vertices()
        faces = builder.create_box_faces()
        
        # 保存模型
        success = builder.save_obj_file(output_path)
        
        if success:
            return True, f"模型已保存到: {output_path}"
        else:
            return False, "模型保存失败"
            
    except Exception as e:
        return False, f"处理错误: {str(e)}"
```

## 方案4: TCP/IP通信

### 4.1 Python服务器

创建一个Python TCP服务器，接收LabVIEW的请求：

```python
import socket
import json
import threading

class CubeVisualizerServer:
    def __init__(self, host='localhost', port=8888):
        self.host = host
        self.port = port
        
    def start_server(self):
        server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server.bind((self.host, self.port))
        server.listen(5)
        
        while True:
            client, addr = server.accept()
            thread = threading.Thread(target=self.handle_client, args=(client,))
            thread.start()
    
    def handle_client(self, client):
        try:
            # 接收请求
            request = client.recv(4096).decode('utf-8')
            data = json.loads(request)
            
            # 处理请求
            result = self.process_request(data)
            
            # 发送响应
            response = json.dumps(result)
            client.send(response.encode('utf-8'))
            
        except Exception as e:
            error_response = {"success": False, "error": str(e)}
            client.send(json.dumps(error_response).encode('utf-8'))
        finally:
            client.close()
```

### 4.2 LabVIEW TCP客户端

使用LabVIEW的TCP VIs与Python服务器通信：

1. **TCP Open Connection**
2. **TCP Write** (发送JSON请求)
3. **TCP Read** (接收JSON响应)
4. **TCP Close Connection**

## 实际使用建议

### 推荐方案选择

1. **简单项目**: 使用方案1 (System Exec)
2. **复杂参数**: 使用方案2 (JSON文件)
3. **实时交互**: 使用方案3 (Python Node)
4. **分布式系统**: 使用方案4 (TCP/IP)

### 性能考虑

- **System Exec**: 每次调用启动新进程，适合偶尔使用
- **Python Node**: 保持Python环境，适合频繁调用
- **TCP服务器**: 最高性能，适合大量并发

### 错误处理

1. **超时设置**: 设置合理的超时时间
2. **路径检查**: 验证所有文件路径
3. **返回码检查**: 检查Python脚本返回码
4. **日志记录**: 记录执行日志便于调试

### 部署注意事项

1. **Python环境**: 确保目标机器有Python环境
2. **依赖包**: 预装所需的Python包
3. **路径配置**: 使用绝对路径避免路径问题
4. **权限设置**: 确保有文件读写权限

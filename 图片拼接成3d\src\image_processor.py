"""
图像处理模块
负责图片的读取、预处理、尺寸标准化等功能
"""

import cv2
import numpy as np
from typing import Dict, Tuple, Optional, Union
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageProcessor:
    """图像处理器类"""
    
    def __init__(self, target_size: Optional[Tuple[int, int]] = None):
        """
        初始化图像处理器
        
        Args:
            target_size: 目标图片尺寸 (width, height)，如果为None则自动计算
        """
        self.target_size = target_size
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    
    def load_image(self, image_path: Union[str, Path]) -> Optional[np.ndarray]:
        """
        加载单张图片
        
        Args:
            image_path: 图片路径
            
        Returns:
            加载的图片数组，失败返回None
        """
        try:
            image_path = Path(image_path)
            
            # 检查文件是否存在
            if not image_path.exists():
                logger.error(f"图片文件不存在: {image_path}")
                return None
            
            # 检查文件格式
            if image_path.suffix.lower() not in self.supported_formats:
                logger.error(f"不支持的图片格式: {image_path.suffix}")
                return None
            
            # 读取图片
            image = cv2.imread(str(image_path))
            if image is None:
                logger.error(f"无法读取图片: {image_path}")
                return None
            
            # 转换为RGB格式（OpenCV默认是BGR）
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            logger.info(f"成功加载图片: {image_path}, 尺寸: {image.shape}")
            return image
            
        except Exception as e:
            logger.error(f"加载图片时发生错误: {e}")
            return None
    
    def load_cube_images(self, image_paths: Dict[str, Union[str, Path]]) -> Dict[str, np.ndarray]:
        """
        加载长方体的5张面图片
        
        Args:
            image_paths: 包含5个面图片路径的字典
                        键: 'front', 'back', 'left', 'right', 'top'
                        值: 图片路径
        
        Returns:
            加载成功的图片字典
        """
        required_faces = {'front', 'back', 'left', 'right', 'top'}
        loaded_images = {}
        
        # 检查是否包含所有必需的面
        missing_faces = required_faces - set(image_paths.keys())
        if missing_faces:
            logger.warning(f"缺少以下面的图片: {missing_faces}")
        
        # 加载每张图片
        for face, path in image_paths.items():
            if face in required_faces:
                image = self.load_image(path)
                if image is not None:
                    loaded_images[face] = image
                else:
                    logger.error(f"无法加载{face}面图片: {path}")
            else:
                logger.warning(f"未知的面名称: {face}")
        
        logger.info(f"成功加载 {len(loaded_images)} 张图片")
        return loaded_images
    
    def resize_image(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        调整图片尺寸
        
        Args:
            image: 输入图片
            target_size: 目标尺寸 (width, height)
            
        Returns:
            调整后的图片
        """
        return cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)
    
    def calculate_optimal_size(self, images: Dict[str, np.ndarray]) -> Tuple[int, int]:
        """
        计算最优的统一尺寸
        
        Args:
            images: 图片字典
            
        Returns:
            最优尺寸 (width, height)
        """
        if not images:
            return (512, 512)  # 默认尺寸
        
        # 收集所有图片的尺寸
        sizes = []
        for image in images.values():
            h, w = image.shape[:2]
            sizes.append((w, h))
        
        # 计算平均尺寸
        avg_width = int(np.mean([s[0] for s in sizes]))
        avg_height = int(np.mean([s[1] for s in sizes]))
        
        # 确保尺寸是偶数（有利于某些处理）
        avg_width = avg_width if avg_width % 2 == 0 else avg_width + 1
        avg_height = avg_height if avg_height % 2 == 0 else avg_height + 1
        
        logger.info(f"计算得到最优尺寸: {avg_width}x{avg_height}")
        return (avg_width, avg_height)
    
    def normalize_images(self, images: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        标准化图片尺寸
        
        Args:
            images: 输入图片字典
            
        Returns:
            标准化后的图片字典
        """
        if not images:
            return {}
        
        # 确定目标尺寸
        if self.target_size is None:
            target_size = self.calculate_optimal_size(images)
        else:
            target_size = self.target_size
        
        # 调整所有图片到目标尺寸
        normalized_images = {}
        for face, image in images.items():
            normalized_images[face] = self.resize_image(image, target_size)
            logger.info(f"{face}面图片已调整到 {target_size}")
        
        return normalized_images
    
    def enhance_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像增强处理
        
        Args:
            image: 输入图片
            
        Returns:
            增强后的图片
        """
        # 直方图均衡化
        lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
        lab[:, :, 0] = cv2.equalizeHist(lab[:, :, 0])
        enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
        
        return enhanced
    
    def process_cube_images(self, image_paths: Dict[str, Union[str, Path]], 
                          enhance: bool = False) -> Dict[str, np.ndarray]:
        """
        处理长方体图片的完整流程
        
        Args:
            image_paths: 图片路径字典
            enhance: 是否进行图像增强
            
        Returns:
            处理后的图片字典
        """
        # 加载图片
        images = self.load_cube_images(image_paths)
        
        if not images:
            logger.error("没有成功加载任何图片")
            return {}
        
        # 标准化尺寸
        normalized_images = self.normalize_images(images)
        
        # 可选的图像增强
        if enhance:
            enhanced_images = {}
            for face, image in normalized_images.items():
                enhanced_images[face] = self.enhance_image(image)
            return enhanced_images
        
        return normalized_images
    
    def save_processed_images(self, images: Dict[str, np.ndarray], 
                            output_dir: Union[str, Path]) -> Dict[str, str]:
        """
        保存处理后的图片
        
        Args:
            images: 处理后的图片字典
            output_dir: 输出目录
            
        Returns:
            保存的文件路径字典
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        saved_paths = {}
        for face, image in images.items():
            # 转换回BGR格式用于保存
            bgr_image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
            
            # 保存文件
            output_path = output_dir / f"{face}_processed.png"
            cv2.imwrite(str(output_path), bgr_image)
            saved_paths[face] = str(output_path)
            
            logger.info(f"已保存{face}面图片到: {output_path}")
        
        return saved_paths


if __name__ == "__main__":
    # 测试代码
    processor = ImageProcessor()
    
    # 示例用法
    test_paths = {
        'front': 'images/front.jpg',
        'back': 'images/back.jpg',
        'left': 'images/left.jpg',
        'right': 'images/right.jpg',
        'top': 'images/top.jpg'
    }
    
    processed_images = processor.process_cube_images(test_paths, enhance=True)
    print(f"处理完成，共处理 {len(processed_images)} 张图片")

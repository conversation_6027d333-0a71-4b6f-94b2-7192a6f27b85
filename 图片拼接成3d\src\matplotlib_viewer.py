"""
基于matplotlib的3D查看器
替代Open3D，提供基本的3D可视化功能
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import matplotlib.patches as patches
from typing import Dict, Tuple, Optional, List
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MatplotlibViewer:
    """基于matplotlib的3D查看器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        """
        初始化查看器
        
        Args:
            figsize: 图形尺寸
        """
        self.figsize = figsize
        self.fig = None
        self.ax = None
        
    def create_cube_faces(self, vertices: np.ndarray) -> List[np.ndarray]:
        """
        创建立方体的6个面
        
        Args:
            vertices: 8个顶点坐标
            
        Returns:
            6个面的顶点列表
        """
        # 定义6个面，每个面4个顶点
        faces = [
            [vertices[0], vertices[1], vertices[2], vertices[3]],  # 前面
            [vertices[4], vertices[7], vertices[6], vertices[5]],  # 后面
            [vertices[0], vertices[4], vertices[7], vertices[3]],  # 左面
            [vertices[1], vertices[2], vertices[6], vertices[5]],  # 右面
            [vertices[0], vertices[1], vertices[5], vertices[4]],  # 底面
            [vertices[3], vertices[2], vertices[6], vertices[7]],  # 顶面
        ]
        
        return faces
    
    def get_face_colors(self, images: Dict[str, np.ndarray]) -> List[Tuple[float, float, float, float]]:
        """
        从图片计算面的平均颜色
        
        Args:
            images: 图片字典
            
        Returns:
            颜色列表 (RGBA)
        """
        face_names = ['front', 'back', 'left', 'right', 'bottom', 'top']
        colors = []
        
        for face_name in face_names:
            if face_name in images:
                # 计算图片的平均颜色
                image = images[face_name]
                mean_color = np.mean(image.reshape(-1, 3), axis=0) / 255.0
                colors.append((*mean_color, 0.8))  # 添加透明度
            else:
                # 默认颜色
                colors.append((0.7, 0.7, 0.7, 0.8))
        
        return colors
    
    def display_cube(self, vertices: np.ndarray, 
                    images: Optional[Dict[str, np.ndarray]] = None,
                    show_wireframe: bool = True,
                    show_axes: bool = True) -> None:
        """
        显示3D立方体
        
        Args:
            vertices: 顶点坐标
            images: 图片字典（用于计算颜色）
            show_wireframe: 是否显示线框
            show_axes: 是否显示坐标轴
        """
        # 创建图形
        self.fig = plt.figure(figsize=self.figsize)
        self.ax = self.fig.add_subplot(111, projection='3d')
        
        # 创建面
        faces = self.create_cube_faces(vertices)
        
        # 获取颜色
        if images:
            colors = self.get_face_colors(images)
        else:
            # 默认颜色
            colors = [
                (1.0, 0.4, 0.4, 0.8),  # 前面 - 红色
                (0.4, 1.0, 0.4, 0.8),  # 后面 - 绿色
                (0.4, 0.4, 1.0, 0.8),  # 左面 - 蓝色
                (1.0, 1.0, 0.4, 0.8),  # 右面 - 黄色
                (1.0, 0.4, 1.0, 0.8),  # 底面 - 紫色
                (0.4, 1.0, 1.0, 0.8),  # 顶面 - 青色
            ]
        
        # 绘制面
        face_collection = Poly3DCollection(faces, facecolors=colors, alpha=0.8)
        self.ax.add_collection3d(face_collection)
        
        # 绘制线框
        if show_wireframe:
            # 定义边
            edges = [
                [0, 1], [1, 2], [2, 3], [3, 0],  # 前面
                [4, 5], [5, 6], [6, 7], [7, 4],  # 后面
                [0, 4], [1, 5], [2, 6], [3, 7],  # 连接边
            ]
            
            for edge in edges:
                points = vertices[edge]
                self.ax.plot3D(*points.T, 'k-', alpha=0.6, linewidth=1)
        
        # 设置坐标轴
        self.ax.set_xlabel('X')
        self.ax.set_ylabel('Y')
        self.ax.set_zlabel('Z')
        
        # 设置相等的坐标轴比例
        max_range = np.array([vertices[:, 0].max() - vertices[:, 0].min(),
                             vertices[:, 1].max() - vertices[:, 1].min(),
                             vertices[:, 2].max() - vertices[:, 2].min()]).max() / 2.0
        
        mid_x = (vertices[:, 0].max() + vertices[:, 0].min()) * 0.5
        mid_y = (vertices[:, 1].max() + vertices[:, 1].min()) * 0.5
        mid_z = (vertices[:, 2].max() + vertices[:, 2].min()) * 0.5
        
        self.ax.set_xlim(mid_x - max_range, mid_x + max_range)
        self.ax.set_ylim(mid_y - max_range, mid_y + max_range)
        self.ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # 设置标题
        self.ax.set_title('3D长方体模型', fontsize=14, fontweight='bold')
        
        # 显示坐标轴
        if show_axes:
            self.ax.set_xlabel('X轴', fontsize=12)
            self.ax.set_ylabel('Y轴', fontsize=12)
            self.ax.set_zlabel('Z轴', fontsize=12)
        
        logger.info("3D模型显示完成")
    
    def display_texture_preview(self, images: Dict[str, np.ndarray]) -> None:
        """
        显示纹理预览
        
        Args:
            images: 图片字典
        """
        if not images:
            return
        
        # 创建子图
        num_images = len(images)
        cols = min(3, num_images)
        rows = (num_images + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(cols * 3, rows * 3))
        if num_images == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        # 显示每张图片
        for i, (face_name, image) in enumerate(images.items()):
            row = i // cols
            col = i % cols
            
            if rows > 1:
                ax = axes[row, col]
            else:
                ax = axes[col]
            
            ax.imshow(image)
            ax.set_title(f'{face_name.upper()}面', fontsize=12, fontweight='bold')
            ax.axis('off')
        
        # 隐藏多余的子图
        for i in range(num_images, rows * cols):
            row = i // cols
            col = i % cols
            if rows > 1:
                axes[row, col].axis('off')
            else:
                axes[col].axis('off')
        
        plt.tight_layout()
        plt.suptitle('纹理预览', fontsize=16, fontweight='bold', y=1.02)
        
        logger.info("纹理预览显示完成")
    
    def save_figure(self, filename: str, dpi: int = 300) -> bool:
        """
        保存图形
        
        Args:
            filename: 文件名
            dpi: 分辨率
            
        Returns:
            是否成功保存
        """
        try:
            if self.fig:
                self.fig.savefig(filename, dpi=dpi, bbox_inches='tight')
                logger.info(f"图形已保存: {filename}")
                return True
            else:
                logger.error("没有可保存的图形")
                return False
        except Exception as e:
            logger.error(f"保存图形失败: {e}")
            return False
    
    def show(self) -> None:
        """显示图形"""
        if self.fig:
            plt.show()
        else:
            logger.error("没有可显示的图形")
    
    def close(self) -> None:
        """关闭图形"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None


class InteractiveMatplotlibViewer(MatplotlibViewer):
    """交互式matplotlib查看器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 8)):
        super().__init__(figsize)
        self.current_elevation = 20
        self.current_azimuth = 45
    
    def setup_interactive_controls(self) -> None:
        """设置交互控制"""
        if not self.ax:
            return
        
        # 设置初始视角
        self.ax.view_init(elev=self.current_elevation, azim=self.current_azimuth)
        
        # 添加按键事件处理
        def on_key(event):
            if event.key == 'left':
                self.current_azimuth -= 10
            elif event.key == 'right':
                self.current_azimuth += 10
            elif event.key == 'up':
                self.current_elevation += 10
            elif event.key == 'down':
                self.current_elevation -= 10
            elif event.key == 'r':
                self.current_elevation = 20
                self.current_azimuth = 45
            
            self.ax.view_init(elev=self.current_elevation, azim=self.current_azimuth)
            self.fig.canvas.draw()
        
        self.fig.canvas.mpl_connect('key_press_event', on_key)
        
        # 添加控制说明
        control_text = """
        控制说明:
        ← → : 水平旋转
        ↑ ↓ : 垂直旋转  
        R   : 重置视角
        鼠标: 拖拽旋转
        """
        
        self.fig.text(0.02, 0.98, control_text, transform=self.fig.transFigure,
                     fontsize=10, verticalalignment='top',
                     bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        logger.info("交互控制已设置")


def create_demo_with_matplotlib():
    """使用matplotlib创建演示"""
    print("使用matplotlib创建3D可视化演示...")
    
    # 创建几何体
    from geometry_builder import GeometryBuilder
    
    builder = GeometryBuilder(dimensions=(2.0, 1.5, 1.0))
    vertices = builder.create_box_vertices()
    
    # 创建演示图片
    import cv2
    face_colors = {
        'front': [255, 100, 100],   # 红色
        'back': [100, 255, 100],    # 绿色
        'left': [100, 100, 255],    # 蓝色
        'right': [255, 255, 100],   # 黄色
        'top': [255, 100, 255],     # 紫色
    }
    
    images = {}
    for face, color in face_colors.items():
        image = np.full((256, 256, 3), color, dtype=np.uint8)
        cv2.putText(image, face.upper(), (80, 128), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        images[face] = image
    
    # 创建查看器
    viewer = InteractiveMatplotlibViewer(figsize=(15, 10))
    
    # 显示纹理预览
    viewer.display_texture_preview(images)
    
    # 显示3D模型
    viewer.display_cube(vertices, images, show_wireframe=True)
    viewer.setup_interactive_controls()
    
    # 保存图形
    viewer.save_figure("output/matplotlib_3d_model.png")
    
    # 显示
    viewer.show()
    
    print("matplotlib演示完成!")


if __name__ == "__main__":
    create_demo_with_matplotlib()

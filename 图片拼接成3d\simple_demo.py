"""
简化演示脚本
不依赖Open3D，仅演示图像处理和几何计算功能
"""

import sys
import os
import numpy as np
import cv2
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))


def create_demo_images():
    """创建演示图片"""
    print("创建演示图片...")
    
    # 创建images目录
    images_dir = Path("images/demo")
    images_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义每个面的颜色
    face_colors = {
        'front': [255, 100, 100],   # 红色
        'back': [100, 255, 100],    # 绿色
        'left': [100, 100, 255],    # 蓝色
        'right': [255, 255, 100],   # 黄色
        'top': [255, 100, 255],     # 紫色
    }
    
    image_paths = {}
    
    for face, color in face_colors.items():
        # 创建512x512的纯色图片
        image = np.full((512, 512, 3), color, dtype=np.uint8)
        
        # 添加文字标识
        cv2.putText(image, face.upper(), (150, 256), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 添加边框
        cv2.rectangle(image, (10, 10), (501, 501), (255, 255, 255), 5)
        
        # 保存图片
        image_path = images_dir / f"{face}.png"
        cv2.imwrite(str(image_path), image)
        image_paths[face] = str(image_path)
        print(f"  ✓ 创建 {face} 面图片: {image_path}")
    
    return image_paths


def test_image_processing():
    """测试图像处理（不依赖Open3D）"""
    print("\n测试图像处理模块...")
    
    try:
        # 简化的图像处理器
        class SimpleImageProcessor:
            def __init__(self):
                self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp'}
            
            def load_image(self, image_path):
                """加载单张图片"""
                try:
                    image_path = Path(image_path)
                    if not image_path.exists():
                        return None
                    
                    if image_path.suffix.lower() not in self.supported_formats:
                        return None
                    
                    image = cv2.imread(str(image_path))
                    if image is None:
                        return None
                    
                    # 转换为RGB格式
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    return image
                except:
                    return None
            
            def load_cube_images(self, image_paths):
                """加载长方体的5张面图片"""
                loaded_images = {}
                required_faces = {'front', 'back', 'left', 'right', 'top'}
                
                for face, path in image_paths.items():
                    if face in required_faces:
                        image = self.load_image(path)
                        if image is not None:
                            loaded_images[face] = image
                
                return loaded_images
            
            def resize_image(self, image, target_size):
                """调整图片尺寸"""
                return cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)
            
            def normalize_images(self, images):
                """标准化图片尺寸"""
                if not images:
                    return {}
                
                # 计算平均尺寸
                sizes = []
                for image in images.values():
                    h, w = image.shape[:2]
                    sizes.append((w, h))
                
                avg_width = int(np.mean([s[0] for s in sizes]))
                avg_height = int(np.mean([s[1] for s in sizes]))
                target_size = (avg_width, avg_height)
                
                # 调整所有图片到目标尺寸
                normalized_images = {}
                for face, image in images.items():
                    normalized_images[face] = self.resize_image(image, target_size)
                
                return normalized_images
        
        # 创建演示图片
        image_paths = create_demo_images()
        
        # 创建图像处理器
        processor = SimpleImageProcessor()
        
        # 加载图片
        images = processor.load_cube_images(image_paths)
        print(f"  ✓ 成功加载 {len(images)} 张图片")
        
        # 标准化尺寸
        normalized_images = processor.normalize_images(images)
        print(f"  ✓ 成功标准化 {len(normalized_images)} 张图片")
        
        # 检查图片尺寸
        for face, image in normalized_images.items():
            print(f"    {face}: {image.shape}")
        
        return normalized_images
        
    except Exception as e:
        print(f"  ✗ 图像处理失败: {e}")
        return None


def test_geometry_calculation():
    """测试几何计算（不依赖Open3D）"""
    print("\n测试几何计算模块...")
    
    try:
        # 简化的几何建模器
        class SimpleGeometryBuilder:
            def __init__(self, dimensions=(2.0, 2.0, 2.0)):
                self.width, self.height, self.depth = dimensions
            
            def create_box_vertices(self):
                """创建长方体的8个顶点"""
                w, h, d = self.width / 2, self.height / 2, self.depth / 2
                
                vertices = np.array([
                    [-w, -h,  d],  # 0: 左下前
                    [ w, -h,  d],  # 1: 右下前
                    [ w,  h,  d],  # 2: 右上前
                    [-w,  h,  d],  # 3: 左上前
                    [-w, -h, -d],  # 4: 左下后
                    [ w, -h, -d],  # 5: 右下后
                    [ w,  h, -d],  # 6: 右上后
                    [-w,  h, -d],  # 7: 左上后
                ], dtype=np.float32)
                
                return vertices
            
            def create_box_faces(self):
                """创建长方体的6个面（每个面由2个三角形组成）"""
                faces = np.array([
                    # 前面
                    [0, 1, 2], [0, 2, 3],
                    # 后面
                    [4, 7, 6], [4, 6, 5],
                    # 左面
                    [4, 0, 3], [4, 3, 7],
                    # 右面
                    [1, 5, 6], [1, 6, 2],
                    # 底面
                    [4, 5, 1], [4, 1, 0],
                    # 顶面
                    [3, 2, 6], [3, 6, 7],
                ], dtype=np.int32)
                
                return faces
            
            def get_face_centers(self):
                """计算每个面的中心点"""
                vertices = self.create_box_vertices()
                
                face_centers = {
                    'front': np.mean(vertices[[0, 1, 2, 3]], axis=0),
                    'back': np.mean(vertices[[4, 5, 6, 7]], axis=0),
                    'left': np.mean(vertices[[0, 3, 4, 7]], axis=0),
                    'right': np.mean(vertices[[1, 2, 5, 6]], axis=0),
                    'bottom': np.mean(vertices[[0, 1, 4, 5]], axis=0),
                    'top': np.mean(vertices[[2, 3, 6, 7]], axis=0),
                }
                
                return face_centers
        
        # 创建几何建模器
        builder = SimpleGeometryBuilder(dimensions=(2.0, 1.5, 1.0))
        
        # 创建顶点
        vertices = builder.create_box_vertices()
        print(f"  ✓ 创建 {len(vertices)} 个顶点")
        
        # 创建面
        faces = builder.create_box_faces()
        print(f"  ✓ 创建 {len(faces)} 个三角形面")
        
        # 计算面中心
        face_centers = builder.get_face_centers()
        print(f"  ✓ 计算 {len(face_centers)} 个面的中心点")
        
        # 显示面中心坐标
        for face_name, center in face_centers.items():
            print(f"    {face_name}: ({center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f})")
        
        return builder
        
    except Exception as e:
        print(f"  ✗ 几何计算失败: {e}")
        return None


def create_texture_atlas(images):
    """创建简化的纹理图集"""
    print("\n创建纹理图集...")
    
    try:
        if not images:
            return None
        
        # 获取图片尺寸
        sample_image = next(iter(images.values()))
        img_height, img_width = sample_image.shape[:2]
        
        # 创建2x3的图集布局
        atlas_width = img_width * 3
        atlas_height = img_height * 2
        atlas = np.zeros((atlas_height, atlas_width, 3), dtype=np.uint8)
        
        # 布局位置
        positions = {
            'left': (0, 0),
            'front': (1, 0),
            'right': (2, 0),
            'back': (0, 1),
            'top': (1, 1),
        }
        
        # 放置图片
        for face_name, image in images.items():
            if face_name in positions:
                col, row = positions[face_name]
                start_x = col * img_width
                start_y = row * img_height
                end_x = start_x + img_width
                end_y = start_y + img_height
                
                atlas[start_y:end_y, start_x:end_x] = image
                print(f"  ✓ 放置 {face_name} 面到图集位置 ({col}, {row})")
        
        # 保存图集
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        atlas_path = output_dir / "texture_atlas.png"
        atlas_bgr = cv2.cvtColor(atlas, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(atlas_path), atlas_bgr)
        print(f"  ✓ 纹理图集已保存: {atlas_path}")
        
        return atlas
        
    except Exception as e:
        print(f"  ✗ 创建纹理图集失败: {e}")
        return None


def save_obj_file(vertices, faces, output_path):
    """保存简化的OBJ文件"""
    print(f"\n保存OBJ文件: {output_path}")
    
    try:
        with open(output_path, 'w') as f:
            # 写入文件头
            f.write("# 3D长方体模型\n")
            f.write("# 由3D可视化系统生成\n\n")
            
            # 写入顶点
            f.write("# 顶点坐标\n")
            for i, vertex in enumerate(vertices):
                f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
            
            f.write("\n# 面定义\n")
            # 写入面（OBJ文件索引从1开始）
            for i, face in enumerate(faces):
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        print(f"  ✓ OBJ文件已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"  ✗ 保存OBJ文件失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("3D长方体可视化系统 - 简化演示")
    print("=" * 60)
    
    try:
        # 测试图像处理
        processed_images = test_image_processing()
        if not processed_images:
            print("✗ 图像处理测试失败")
            return
        
        # 测试几何计算
        builder = test_geometry_calculation()
        if not builder:
            print("✗ 几何计算测试失败")
            return
        
        # 创建纹理图集
        atlas = create_texture_atlas(processed_images)
        
        # 保存3D模型
        vertices = builder.create_box_vertices()
        faces = builder.create_box_faces()
        
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        obj_path = output_dir / "demo_cube.obj"
        save_obj_file(vertices, faces, obj_path)
        
        print("\n" + "=" * 60)
        print("✓ 简化演示完成!")
        print("=" * 60)
        
        print("\n生成的文件:")
        print(f"  - 演示图片: images/demo/")
        print(f"  - 纹理图集: output/texture_atlas.png")
        print(f"  - 3D模型: output/demo_cube.obj")
        
        print("\n说明:")
        print("  这是一个简化版本的演示，展示了核心功能：")
        print("  1. ✓ 图像处理和加载")
        print("  2. ✓ 几何体顶点和面计算")
        print("  3. ✓ 纹理图集创建")
        print("  4. ✓ 3D模型文件导出")
        
        print("\n完整功能需要安装Open3D:")
        print("  pip install open3d")
        print("  然后运行: python examples/basic_example.py")
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

"""
LabVIEW集成示例
演示如何从LabVIEW调用Python脚本
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from labview_interface import LabVIEWInterface
from cube_3d_visualizer import create_demo_images
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def simulate_labview_call():
    """模拟LabVIEW调用Python脚本"""
    print("=== 模拟LabVIEW调用示例 ===")
    
    # 1. 创建演示图片（模拟LabVIEW传入的图片路径）
    demo_images = create_demo_images("../images/labview_demo")
    
    # 2. 创建LabVIEW接口
    interface = LabVIEWInterface()
    
    try:
        # 3. 处理图片并创建3D模型
        print("处理图片并创建3D模型...")
        mesh = interface.process_and_display(
            image_paths=demo_images,
            dimensions=(2.5, 2.0, 1.5),
            enhance=True,
            output_path="../output/labview_result.obj",
            output_format="obj",
            display=False  # LabVIEW调用时通常不显示
        )
        
        if mesh is not None:
            print("✓ 3D模型创建成功")
            print(f"✓ 模型已保存: ../output/labview_result.obj")
            print(f"✓ JSON信息已保存: ../output/labview_result.json")
        else:
            print("✗ 3D模型创建失败")
            
    except Exception as e:
        logger.error(f"处理失败: {e}")
    finally:
        # 4. 清理资源
        interface.cleanup()


def command_line_example():
    """命令行调用示例"""
    print("\n=== 命令行调用示例 ===")
    
    # 创建演示图片
    demo_images = create_demo_images("../images/cmd_demo")
    
    # 构建命令行参数
    cmd_args = [
        "python", "../src/labview_interface.py",
        "--front", demo_images['front'],
        "--back", demo_images['back'],
        "--left", demo_images['left'],
        "--right", demo_images['right'],
        "--top", demo_images['top'],
        "--width", "3.0",
        "--height", "2.5",
        "--depth", "2.0",
        "--enhance",
        "--output", "../output/cmd_result.ply",
        "--format", "ply",
        "--no-display"
    ]
    
    print("命令行调用:")
    print(" ".join(cmd_args))
    
    # 在实际应用中，LabVIEW会使用System Exec调用这个命令
    print("\n在LabVIEW中，您可以使用System Exec VI调用上述命令")


def json_communication_example():
    """JSON通信示例（用于复杂参数传递）"""
    print("\n=== JSON通信示例 ===")
    
    # 创建配置文件
    config = {
        "input_images": {
            "front": "../images/json_demo/front.jpg",
            "back": "../images/json_demo/back.jpg",
            "left": "../images/json_demo/left.jpg",
            "right": "../images/json_demo/right.jpg",
            "top": "../images/json_demo/top.jpg"
        },
        "dimensions": {
            "width": 4.0,
            "height": 3.0,
            "depth": 2.5
        },
        "processing_options": {
            "enhance": True,
            "use_atlas": True,
            "target_image_size": [1024, 1024]
        },
        "output_options": {
            "model_path": "../output/json_result.obj",
            "format": "obj",
            "export_images": True,
            "export_atlas": True,
            "display": False
        }
    }
    
    # 保存配置文件
    config_path = "../output/labview_config.json"
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"配置文件已创建: {config_path}")
    print("LabVIEW可以通过读写JSON文件与Python脚本通信")
    
    # 创建演示图片
    demo_images = create_demo_images("../images/json_demo")
    
    # 模拟处理配置文件
    process_json_config(config_path)


def process_json_config(config_path):
    """处理JSON配置文件"""
    try:
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建接口
        interface = LabVIEWInterface()
        
        # 处理图片
        mesh = interface.process_and_display(
            image_paths=config["input_images"],
            dimensions=(
                config["dimensions"]["width"],
                config["dimensions"]["height"],
                config["dimensions"]["depth"]
            ),
            enhance=config["processing_options"]["enhance"],
            output_path=config["output_options"]["model_path"],
            output_format=config["output_options"]["format"],
            display=config["output_options"]["display"]
        )
        
        # 创建结果文件
        result = {
            "success": mesh is not None,
            "output_files": {
                "model": config["output_options"]["model_path"],
                "info": config["output_options"]["model_path"].replace(".obj", ".json")
            },
            "model_info": interface.geometry_builder.get_face_info() if mesh else None
        }
        
        # 保存结果
        result_path = config_path.replace("_config.json", "_result.json")
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"处理结果已保存: {result_path}")
        
        # 清理
        interface.cleanup()
        
    except Exception as e:
        logger.error(f"处理JSON配置失败: {e}")


def batch_processing_example():
    """批处理示例"""
    print("\n=== 批处理示例 ===")
    
    # 模拟多个长方体的批处理
    batch_configs = [
        {
            "name": "cube1",
            "dimensions": (2.0, 2.0, 2.0),
            "enhance": False
        },
        {
            "name": "cube2", 
            "dimensions": (3.0, 2.0, 1.0),
            "enhance": True
        },
        {
            "name": "cube3",
            "dimensions": (1.5, 3.0, 2.5),
            "enhance": False
        }
    ]
    
    results = []
    
    for i, config in enumerate(batch_configs):
        print(f"处理第 {i+1} 个长方体: {config['name']}")
        
        # 创建演示图片
        demo_images = create_demo_images(f"../images/batch_{config['name']}")
        
        # 创建接口
        interface = LabVIEWInterface()
        
        try:
            # 处理
            mesh = interface.process_and_display(
                image_paths=demo_images,
                dimensions=config["dimensions"],
                enhance=config["enhance"],
                output_path=f"../output/batch_{config['name']}.obj",
                display=False
            )
            
            results.append({
                "name": config['name'],
                "success": mesh is not None,
                "output_path": f"../output/batch_{config['name']}.obj"
            })
            
        except Exception as e:
            logger.error(f"处理 {config['name']} 失败: {e}")
            results.append({
                "name": config['name'],
                "success": False,
                "error": str(e)
            })
        finally:
            interface.cleanup()
    
    # 保存批处理结果
    batch_result_path = "../output/batch_results.json"
    with open(batch_result_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"批处理结果已保存: {batch_result_path}")
    
    # 打印结果摘要
    successful = sum(1 for r in results if r["success"])
    print(f"批处理完成: {successful}/{len(results)} 个成功")


def error_handling_example():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    interface = LabVIEWInterface()
    
    # 测试各种错误情况
    error_cases = [
        {
            "name": "文件不存在",
            "image_paths": {
                'front': 'nonexistent.jpg',
                'back': 'nonexistent.jpg',
                'left': 'nonexistent.jpg',
                'right': 'nonexistent.jpg',
                'top': 'nonexistent.jpg'
            }
        },
        {
            "name": "缺少图片",
            "image_paths": {
                'front': '../images/demo/front.png',
                'back': '../images/demo/back.png'
                # 缺少其他面
            }
        }
    ]
    
    for case in error_cases:
        print(f"测试错误情况: {case['name']}")
        try:
            mesh = interface.process_and_display(
                image_paths=case["image_paths"],
                display=False
            )
            if mesh is None:
                print(f"  ✓ 正确处理了错误: {case['name']}")
            else:
                print(f"  ✗ 未正确处理错误: {case['name']}")
        except Exception as e:
            print(f"  ✓ 捕获到异常: {e}")
    
    interface.cleanup()


def main():
    """主函数"""
    print("LabVIEW集成示例程序")
    print("=" * 50)
    
    # 创建输出目录
    output_dir = Path("../output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 运行各种示例
        simulate_labview_call()
        command_line_example()
        json_communication_example()
        batch_processing_example()
        error_handling_example()
        
        print("\n=== LabVIEW集成说明 ===")
        print("1. 命令行调用:")
        print("   在LabVIEW中使用System Exec VI调用Python脚本")
        print("   传递图片路径和参数作为命令行参数")
        
        print("\n2. JSON文件通信:")
        print("   LabVIEW写入配置JSON文件")
        print("   Python读取配置，处理后写入结果JSON文件")
        print("   LabVIEW读取结果JSON文件获取处理结果")
        
        print("\n3. Python Node调用:")
        print("   在LabVIEW中直接调用Python函数")
        print("   需要配置Python环境和模块路径")
        
        print("\n4. 错误处理:")
        print("   检查返回值和日志文件")
        print("   使用try-catch处理异常")
        
    except Exception as e:
        logger.error(f"运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\nLabVIEW集成示例程序结束")


if __name__ == "__main__":
    main()

"""
基础使用示例
演示如何使用3D长方体可视化系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from cube_3d_visualizer import Cube3DVisualizer, create_demo_images
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def basic_usage_example():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 1. 创建演示图片
    print("1. 创建演示图片...")
    demo_dir = Path("../images/demo")
    demo_images = create_demo_images(demo_dir)
    print(f"演示图片已创建: {list(demo_images.keys())}")
    
    # 2. 创建可视化器
    print("2. 创建可视化器...")
    visualizer = Cube3DVisualizer(
        dimensions=(3.0, 2.0, 1.5),  # 长方体尺寸
        target_image_size=(512, 512)  # 目标图片尺寸
    )
    
    # 3. 加载和处理图片
    print("3. 加载和处理图片...")
    processed_images = visualizer.load_and_process_images(
        image_paths=demo_images,
        enhance=False  # 不进行图像增强
    )
    print(f"成功处理 {len(processed_images)} 张图片")
    
    # 4. 创建3D模型
    print("4. 创建3D模型...")
    mesh = visualizer.create_3d_model(use_atlas=False)
    print("3D模型创建完成")
    
    # 5. 保存模型
    print("5. 保存模型...")
    output_dir = Path("../output")
    output_dir.mkdir(exist_ok=True)
    
    success = visualizer.save_model(
        output_path=output_dir / "basic_example.obj",
        format="obj"
    )
    if success:
        print("模型保存成功")
    
    # 6. 显示模型信息
    print("6. 模型信息:")
    info = visualizer.get_model_info()
    print(f"   尺寸: {info['dimensions']}")
    print(f"   顶点数: {info['mesh_stats']['num_vertices']}")
    print(f"   三角形数: {info['mesh_stats']['num_triangles']}")
    
    # 7. 显示交互式3D查看器
    print("7. 显示3D查看器...")
    print("   控制说明:")
    print("   - 鼠标左键拖拽: 旋转")
    print("   - 鼠标右键拖拽: 平移")
    print("   - 鼠标滚轮: 缩放")
    print("   - W键: 切换线框显示")
    print("   - R键: 重置视图")
    print("   - S键: 截屏")
    print("   - ESC键: 退出")
    
    visualizer.display_interactive(
        window_name="基础示例 - 3D长方体",
        window_size=(1024, 768)
    )


def one_line_example():
    """一行代码示例"""
    print("\n=== 一行代码示例 ===")
    
    # 创建演示图片
    demo_images = create_demo_images("../images/demo")
    
    # 一行代码创建和显示
    visualizer = Cube3DVisualizer(dimensions=(2.0, 2.0, 2.0))
    mesh = visualizer.create_and_display(
        image_paths=demo_images,
        enhance=True,  # 启用图像增强
        use_atlas=True,  # 使用纹理图集
        display=True,  # 显示模型
        save_path="../output/one_line_example.ply",  # 保存路径
        save_format="ply"  # 保存格式
    )
    
    print("一行代码示例完成")


def texture_atlas_example():
    """纹理图集示例"""
    print("\n=== 纹理图集示例 ===")
    
    # 创建演示图片
    demo_images = create_demo_images("../images/demo")
    
    # 创建可视化器
    visualizer = Cube3DVisualizer()
    
    # 处理图片
    visualizer.load_and_process_images(demo_images)
    
    # 创建纹理图集
    atlas_path = "../output/texture_atlas.png"
    success = visualizer.create_texture_atlas(atlas_path)
    
    if success:
        print(f"纹理图集已保存: {atlas_path}")
        print(f"UV信息已保存: {atlas_path.replace('.png', '_uv_info.json')}")
    
    # 使用图集创建模型
    mesh = visualizer.create_3d_model(use_atlas=True)
    visualizer.save_model("../output/atlas_example.obj")
    
    print("纹理图集示例完成")


def export_example():
    """导出示例"""
    print("\n=== 导出示例 ===")
    
    # 创建演示图片
    demo_images = create_demo_images("../images/demo")
    
    # 创建可视化器
    visualizer = Cube3DVisualizer()
    
    # 处理图片
    visualizer.load_and_process_images(demo_images, enhance=True)
    
    # 导出处理后的图片
    exported_paths = visualizer.export_images("../output/processed_images")
    print("导出的处理后图片:")
    for face, path in exported_paths.items():
        print(f"   {face}: {path}")
    
    # 创建和保存模型
    mesh = visualizer.create_3d_model()
    
    # 保存为不同格式
    formats = ["obj", "ply", "stl"]
    for fmt in formats:
        output_path = f"../output/export_example.{fmt}"
        success = visualizer.save_model(output_path, fmt)
        if success:
            print(f"已保存为 {fmt.upper()} 格式: {output_path}")
    
    print("导出示例完成")


def custom_images_example():
    """自定义图片示例"""
    print("\n=== 自定义图片示例 ===")
    
    # 这里假设用户有自己的图片
    # 用户需要将图片放在指定位置并修改路径
    
    custom_image_paths = {
        'front': '../images/custom/front.jpg',
        'back': '../images/custom/back.jpg',
        'left': '../images/custom/left.jpg',
        'right': '../images/custom/right.jpg',
        'top': '../images/custom/top.jpg'
    }
    
    # 检查文件是否存在
    missing_files = []
    for face, path in custom_image_paths.items():
        if not Path(path).exists():
            missing_files.append(f"{face}: {path}")
    
    if missing_files:
        print("自定义图片文件不存在，跳过此示例:")
        for missing in missing_files:
            print(f"   {missing}")
        print("请将您的图片放在 ../images/custom/ 目录下")
        return
    
    # 创建可视化器
    visualizer = Cube3DVisualizer(
        dimensions=(4.0, 3.0, 2.0),  # 自定义尺寸
        target_image_size=(1024, 1024)  # 高分辨率
    )
    
    # 处理和显示
    mesh = visualizer.create_and_display(
        image_paths=custom_image_paths,
        enhance=True,  # 启用图像增强
        display=True,
        save_path="../output/custom_example.obj"
    )
    
    print("自定义图片示例完成")


def main():
    """主函数"""
    print("3D长方体可视化系统 - 示例程序")
    print("=" * 50)
    
    # 创建输出目录
    output_dir = Path("../output")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 运行各种示例
        basic_usage_example()
        
        # 询问是否继续其他示例
        response = input("\n是否运行其他示例? (y/n): ")
        if response.lower() == 'y':
            one_line_example()
            texture_atlas_example()
            export_example()
            custom_images_example()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        logger.error(f"运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n示例程序结束")


if __name__ == "__main__":
    main()

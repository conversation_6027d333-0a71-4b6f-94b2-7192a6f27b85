# 安装和使用指南

## 系统要求

- **Python**: 3.8 或更高版本
- **操作系统**: Windows 10/11, macOS, Linux
- **内存**: 建议 4GB 以上
- **显卡**: 支持 OpenGL 3.3 或更高版本（用于3D显示）

## 安装步骤

### 1. 克隆或下载项目
```bash
git clone <repository_url>
cd 图片拼接成3d
```

### 2. 创建虚拟环境（推荐）
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装依赖包
```bash
pip install -r requirements.txt
```

### 4. 验证安装
```bash
# 运行测试
python tests/run_tests.py

# 运行基础示例
python examples/basic_example.py
```

## 快速开始

### 1. 基础使用
```python
from src.cube_3d_visualizer import Cube3DVisualizer

# 创建可视化器
visualizer = Cube3DVisualizer(dimensions=(2.0, 2.0, 2.0))

# 准备图片路径
image_paths = {
    'front': 'path/to/front.jpg',
    'back': 'path/to/back.jpg',
    'left': 'path/to/left.jpg',
    'right': 'path/to/right.jpg',
    'top': 'path/to/top.jpg'
}

# 创建和显示3D模型
mesh = visualizer.create_and_display(image_paths)
```

### 2. 命令行使用
```bash
python src/labview_interface.py \
    --front front.jpg \
    --back back.jpg \
    --left left.jpg \
    --right right.jpg \
    --top top.jpg \
    --output result.obj
```

### 3. LabVIEW集成

#### 方法1: System Exec调用
在LabVIEW中使用System Exec VI调用Python脚本：
```
python src/labview_interface.py --front %front_path% --back %back_path% --left %left_path% --right %right_path% --top %top_path% --output %output_path%
```

#### 方法2: Python Node调用
1. 在LabVIEW中添加Python Node
2. 配置Python环境路径
3. 导入模块并调用函数

#### 方法3: JSON文件通信
1. LabVIEW写入配置JSON文件
2. Python读取配置并处理
3. Python写入结果JSON文件
4. LabVIEW读取结果

## 常见问题

### Q: 安装Open3D时出错
A: 尝试以下解决方案：
```bash
# 更新pip
pip install --upgrade pip

# 使用conda安装（如果有conda）
conda install -c open3d-admin open3d

# 或使用预编译版本
pip install open3d==0.18.0
```

### Q: 显示3D模型时窗口无法打开
A: 检查以下项目：
1. 确保显卡驱动已更新
2. 检查OpenGL支持：
```python
import open3d as o3d
print(o3d.visualization.gui.Application.instance.initialize())
```

### Q: 图片加载失败
A: 检查：
1. 图片路径是否正确
2. 图片格式是否支持（PNG, JPG, BMP, TIFF）
3. 图片文件是否损坏

### Q: LabVIEW调用Python失败
A: 检查：
1. Python环境路径配置
2. 模块导入路径
3. 权限设置
4. 查看日志文件：`cube_3d_visualizer.log`

## 性能优化

### 1. 图片尺寸
- 建议图片尺寸不超过 2048x2048
- 使用相同尺寸的图片可提高处理速度

### 2. 内存使用
- 处理大量图片时，考虑分批处理
- 及时释放不需要的图片数据

### 3. 显示性能
- 降低窗口分辨率可提高显示流畅度
- 关闭不必要的视觉效果

## 高级配置

### 1. 自定义图片处理
```python
from src.image_processor import ImageProcessor

processor = ImageProcessor(target_size=(1024, 1024))
# 自定义处理参数
```

### 2. 自定义几何体
```python
from src.geometry_builder import GeometryBuilder

builder = GeometryBuilder(dimensions=(3.0, 2.0, 1.5))
# 自定义几何参数
```

### 3. 自定义纹理映射
```python
from src.texture_mapper import TextureMapper

mapper = TextureMapper()
# 自定义纹理参数
```

## 故障排除

### 1. 查看日志
日志文件位置：`cube_3d_visualizer.log`

### 2. 调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 3. 测试各个模块
```bash
# 测试图像处理
python tests/test_image_processor.py

# 测试几何建模
python tests/test_geometry_builder.py

# 运行所有测试
python tests/run_tests.py
```

## 技术支持

如果遇到问题，请：
1. 查看日志文件
2. 运行测试脚本
3. 检查系统要求
4. 查看示例代码

## 更新日志

### v0.1.0
- 初始版本
- 基础3D可视化功能
- LabVIEW集成接口
- 图像处理和纹理映射
- 交互式3D查看器

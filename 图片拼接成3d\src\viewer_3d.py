"""
交互式3D查看器
实现鼠标控制的旋转、缩放、平移功能，提供实时3D显示
"""

import open3d as o3d
import numpy as np
from typing import List, Optional, Dict, Callable
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Interactive3DViewer:
    """交互式3D查看器类"""
    
    def __init__(self, window_name: str = "3D长方体查看器", 
                 window_width: int = 1024, window_height: int = 768):
        """
        初始化3D查看器
        
        Args:
            window_name: 窗口名称
            window_width: 窗口宽度
            window_height: 窗口高度
        """
        self.window_name = window_name
        self.window_width = window_width
        self.window_height = window_height
        
        # 创建可视化器
        self.vis = o3d.visualization.Visualizer()
        self.vis.create_window(window_name=window_name, 
                              width=window_width, 
                              height=window_height)
        
        # 几何体列表
        self.geometries = []
        
        # 控制参数
        self.show_wireframe = False
        self.show_axes = True
        self.background_color = [0.1, 0.1, 0.1]  # 深灰色背景
        
        # 设置渲染选项
        self._setup_render_options()
        
        logger.info(f"3D查看器已初始化，窗口尺寸: {window_width}x{window_height}")
    
    def _setup_render_options(self):
        """设置渲染选项"""
        render_option = self.vis.get_render_option()
        
        # 设置背景颜色
        render_option.background_color = np.array(self.background_color)
        
        # 设置点大小和线宽
        render_option.point_size = 3.0
        render_option.line_width = 2.0
        
        # 启用光照
        render_option.light_on = True
        
        # 设置材质属性
        render_option.mesh_show_back_face = True
        render_option.mesh_show_wireframe = self.show_wireframe
        
        logger.info("渲染选项已设置")
    
    def add_geometry(self, geometry: o3d.geometry.Geometry, 
                    name: Optional[str] = None) -> None:
        """
        添加几何体到场景
        
        Args:
            geometry: Open3D几何体
            name: 几何体名称（可选）
        """
        self.vis.add_geometry(geometry)
        self.geometries.append({
            'geometry': geometry,
            'name': name or f"geometry_{len(self.geometries)}",
            'visible': True
        })
        
        logger.info(f"已添加几何体: {name or 'unnamed'}")
    
    def add_cube_mesh(self, mesh: o3d.geometry.TriangleMesh, 
                     wireframe: Optional[o3d.geometry.LineSet] = None) -> None:
        """
        添加长方体网格和可选的线框
        
        Args:
            mesh: 三角网格
            wireframe: 线框（可选）
        """
        # 添加主网格
        self.add_geometry(mesh, "cube_mesh")
        
        # 添加线框（如果提供）
        if wireframe is not None and self.show_wireframe:
            self.add_geometry(wireframe, "cube_wireframe")
    
    def add_coordinate_axes(self, size: float = 1.0) -> None:
        """
        添加坐标轴
        
        Args:
            size: 坐标轴大小
        """
        if self.show_axes:
            axes = o3d.geometry.TriangleMesh.create_coordinate_frame(size=size)
            self.add_geometry(axes, "coordinate_axes")
    
    def set_camera_position(self, position: List[float], 
                          target: List[float] = [0, 0, 0],
                          up: List[float] = [0, 1, 0]) -> None:
        """
        设置相机位置
        
        Args:
            position: 相机位置 [x, y, z]
            target: 观察目标 [x, y, z]
            up: 上方向向量 [x, y, z]
        """
        view_control = self.vis.get_view_control()
        
        # 设置相机参数
        camera_params = view_control.convert_to_pinhole_camera_parameters()
        camera_params.extrinsic = self._look_at_matrix(position, target, up)
        
        view_control.convert_from_pinhole_camera_parameters(camera_params)
        logger.info(f"相机位置已设置: {position}")
    
    def _look_at_matrix(self, eye: List[float], target: List[float], 
                       up: List[float]) -> np.ndarray:
        """
        创建look-at变换矩阵
        
        Args:
            eye: 相机位置
            target: 观察目标
            up: 上方向
            
        Returns:
            4x4变换矩阵
        """
        eye = np.array(eye, dtype=np.float64)
        target = np.array(target, dtype=np.float64)
        up = np.array(up, dtype=np.float64)
        
        # 计算相机坐标系
        forward = target - eye
        forward = forward / np.linalg.norm(forward)
        
        right = np.cross(forward, up)
        right = right / np.linalg.norm(right)
        
        up_new = np.cross(right, forward)
        
        # 构建变换矩阵
        rotation = np.array([
            [right[0], up_new[0], -forward[0]],
            [right[1], up_new[1], -forward[1]],
            [right[2], up_new[2], -forward[2]]
        ])
        
        translation = -rotation @ eye
        
        # 4x4齐次变换矩阵
        transform = np.eye(4)
        transform[:3, :3] = rotation
        transform[:3, 3] = translation
        
        return transform
    
    def set_lighting(self, ambient: float = 0.3, diffuse: float = 0.7) -> None:
        """
        设置光照参数
        
        Args:
            ambient: 环境光强度
            diffuse: 漫反射光强度
        """
        render_option = self.vis.get_render_option()
        # Open3D的光照设置相对简单，这里设置基本参数
        render_option.light_on = True
        logger.info(f"光照参数已设置: ambient={ambient}, diffuse={diffuse}")
    
    def toggle_wireframe(self) -> None:
        """切换线框显示"""
        self.show_wireframe = not self.show_wireframe
        render_option = self.vis.get_render_option()
        render_option.mesh_show_wireframe = self.show_wireframe
        logger.info(f"线框显示: {'开启' if self.show_wireframe else '关闭'}")
    
    def reset_view(self) -> None:
        """重置视图到默认位置"""
        view_control = self.vis.get_view_control()
        view_control.reset_camera_local_rotate()
        
        # 设置默认相机位置
        self.set_camera_position([3, 3, 3], [0, 0, 0], [0, 1, 0])
        logger.info("视图已重置")
    
    def capture_screen(self, filename: str) -> bool:
        """
        截屏保存
        
        Args:
            filename: 保存文件名
            
        Returns:
            是否成功保存
        """
        try:
            self.vis.capture_screen_image(filename)
            logger.info(f"截屏已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"截屏失败: {e}")
            return False
    
    def save_view_point(self, filename: str) -> bool:
        """
        保存视点参数
        
        Args:
            filename: 保存文件名
            
        Returns:
            是否成功保存
        """
        try:
            view_control = self.vis.get_view_control()
            camera_params = view_control.convert_to_pinhole_camera_parameters()
            o3d.io.write_pinhole_camera_parameters(filename, camera_params)
            logger.info(f"视点参数已保存: {filename}")
            return True
        except Exception as e:
            logger.error(f"保存视点参数失败: {e}")
            return False
    
    def load_view_point(self, filename: str) -> bool:
        """
        加载视点参数
        
        Args:
            filename: 参数文件名
            
        Returns:
            是否成功加载
        """
        try:
            camera_params = o3d.io.read_pinhole_camera_parameters(filename)
            view_control = self.vis.get_view_control()
            view_control.convert_from_pinhole_camera_parameters(camera_params)
            logger.info(f"视点参数已加载: {filename}")
            return True
        except Exception as e:
            logger.error(f"加载视点参数失败: {e}")
            return False
    
    def register_key_callback(self, key: int, callback: Callable) -> None:
        """
        注册键盘回调函数
        
        Args:
            key: 按键代码
            callback: 回调函数
        """
        self.vis.register_key_callback(key, callback)
        logger.info(f"已注册按键回调: {key}")
    
    def setup_default_controls(self) -> None:
        """设置默认控制键"""
        
        def toggle_wireframe_callback(vis):
            self.toggle_wireframe()
            return False
        
        def reset_view_callback(vis):
            self.reset_view()
            return False
        
        def capture_screen_callback(vis):
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            self.capture_screen(filename)
            return False
        
        # 注册默认按键
        self.register_key_callback(ord('W'), toggle_wireframe_callback)  # W键切换线框
        self.register_key_callback(ord('R'), reset_view_callback)        # R键重置视图
        self.register_key_callback(ord('S'), capture_screen_callback)    # S键截屏
        
        logger.info("默认控制键已设置: W(线框), R(重置), S(截屏)")
    
    def run(self, setup_controls: bool = True) -> None:
        """
        运行查看器主循环
        
        Args:
            setup_controls: 是否设置默认控制键
        """
        if setup_controls:
            self.setup_default_controls()
        
        # 添加坐标轴
        self.add_coordinate_axes()
        
        # 设置默认相机位置
        self.reset_view()
        
        # 更新几何体
        for geo_info in self.geometries:
            self.vis.update_geometry(geo_info['geometry'])
        
        logger.info("3D查看器开始运行...")
        logger.info("控制说明:")
        logger.info("  鼠标左键拖拽: 旋转")
        logger.info("  鼠标右键拖拽: 平移")
        logger.info("  鼠标滚轮: 缩放")
        logger.info("  W键: 切换线框显示")
        logger.info("  R键: 重置视图")
        logger.info("  S键: 截屏")
        logger.info("  ESC键: 退出")
        
        # 运行可视化循环
        self.vis.run()
        
        # 清理资源
        self.vis.destroy_window()
        logger.info("3D查看器已关闭")
    
    def close(self) -> None:
        """关闭查看器"""
        self.vis.close()
        logger.info("3D查看器已关闭")


class ViewerController:
    """查看器控制器，提供更高级的控制功能"""
    
    def __init__(self, viewer: Interactive3DViewer):
        """
        初始化控制器
        
        Args:
            viewer: 3D查看器实例
        """
        self.viewer = viewer
        self.animation_running = False
        
    def start_rotation_animation(self, speed: float = 1.0) -> None:
        """
        开始旋转动画
        
        Args:
            speed: 旋转速度
        """
        # 注意：Open3D的动画功能有限，这里提供基础实现
        self.animation_running = True
        logger.info(f"旋转动画已开始，速度: {speed}")
    
    def stop_animation(self) -> None:
        """停止动画"""
        self.animation_running = False
        logger.info("动画已停止")
    
    def create_orbit_views(self, num_views: int = 8, radius: float = 5.0) -> List[np.ndarray]:
        """
        创建环绕视图
        
        Args:
            num_views: 视图数量
            radius: 环绕半径
            
        Returns:
            相机位置列表
        """
        positions = []
        for i in range(num_views):
            angle = 2 * np.pi * i / num_views
            x = radius * np.cos(angle)
            z = radius * np.sin(angle)
            y = radius * 0.3  # 稍微抬高视角
            positions.append([x, y, z])
        
        logger.info(f"创建了 {num_views} 个环绕视图")
        return positions


if __name__ == "__main__":
    # 测试代码
    viewer = Interactive3DViewer()

    # 创建测试几何体
    cube = o3d.geometry.TriangleMesh.create_box(width=2.0, height=1.5, depth=1.0)
    cube.paint_uniform_color([0.7, 0.3, 0.3])  # 红色
    cube.compute_vertex_normals()

    # 添加到查看器
    viewer.add_geometry(cube, "test_cube")

    # 运行查看器
    viewer.run()

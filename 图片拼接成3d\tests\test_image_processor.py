"""
图像处理模块测试
"""

import unittest
import numpy as np
import cv2
import tempfile
import os
from pathlib import Path
import sys

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from image_processor import ImageProcessor


class TestImageProcessor(unittest.TestCase):
    """图像处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.processor = ImageProcessor()
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试图片
        self.test_images = self._create_test_images()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_test_images(self):
        """创建测试图片"""
        test_images = {}
        colors = {
            'front': [255, 0, 0],    # 红色
            'back': [0, 255, 0],     # 绿色
            'left': [0, 0, 255],     # 蓝色
            'right': [255, 255, 0],  # 黄色
            'top': [255, 0, 255],    # 紫色
        }
        
        for face, color in colors.items():
            # 创建不同尺寸的图片
            if face == 'front':
                size = (256, 256)
            elif face == 'back':
                size = (512, 512)
            else:
                size = (384, 384)
            
            image = np.full((*size, 3), color, dtype=np.uint8)
            
            # 保存图片
            image_path = os.path.join(self.temp_dir, f"{face}.png")
            cv2.imwrite(image_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
            test_images[face] = image_path
        
        return test_images
    
    def test_load_single_image(self):
        """测试加载单张图片"""
        # 测试正常加载
        image = self.processor.load_image(self.test_images['front'])
        self.assertIsNotNone(image)
        self.assertEqual(len(image.shape), 3)
        self.assertEqual(image.shape[2], 3)  # RGB
        
        # 测试文件不存在
        image = self.processor.load_image("nonexistent.jpg")
        self.assertIsNone(image)
    
    def test_load_cube_images(self):
        """测试加载长方体图片"""
        images = self.processor.load_cube_images(self.test_images)
        
        # 检查加载的图片数量
        self.assertEqual(len(images), 5)
        
        # 检查每张图片
        for face in ['front', 'back', 'left', 'right', 'top']:
            self.assertIn(face, images)
            self.assertIsNotNone(images[face])
    
    def test_resize_image(self):
        """测试图片尺寸调整"""
        image = self.processor.load_image(self.test_images['front'])
        target_size = (512, 512)
        
        resized = self.processor.resize_image(image, target_size)
        
        self.assertEqual(resized.shape[:2], target_size[::-1])  # (height, width)
    
    def test_calculate_optimal_size(self):
        """测试计算最优尺寸"""
        images = self.processor.load_cube_images(self.test_images)
        optimal_size = self.processor.calculate_optimal_size(images)
        
        self.assertIsInstance(optimal_size, tuple)
        self.assertEqual(len(optimal_size), 2)
        self.assertGreater(optimal_size[0], 0)
        self.assertGreater(optimal_size[1], 0)
    
    def test_normalize_images(self):
        """测试图片标准化"""
        images = self.processor.load_cube_images(self.test_images)
        normalized = self.processor.normalize_images(images)
        
        # 检查所有图片尺寸是否一致
        sizes = [img.shape[:2] for img in normalized.values()]
        self.assertTrue(all(size == sizes[0] for size in sizes))
    
    def test_enhance_image(self):
        """测试图像增强"""
        image = self.processor.load_image(self.test_images['front'])
        enhanced = self.processor.enhance_image(image)
        
        self.assertEqual(image.shape, enhanced.shape)
        self.assertIsInstance(enhanced, np.ndarray)
    
    def test_process_cube_images(self):
        """测试完整处理流程"""
        # 不增强
        processed = self.processor.process_cube_images(self.test_images, enhance=False)
        self.assertEqual(len(processed), 5)
        
        # 增强
        enhanced = self.processor.process_cube_images(self.test_images, enhance=True)
        self.assertEqual(len(enhanced), 5)
    
    def test_save_processed_images(self):
        """测试保存处理后的图片"""
        images = self.processor.load_cube_images(self.test_images)
        normalized = self.processor.normalize_images(images)
        
        output_dir = os.path.join(self.temp_dir, "processed")
        saved_paths = self.processor.save_processed_images(normalized, output_dir)
        
        # 检查保存的文件
        self.assertEqual(len(saved_paths), 5)
        for path in saved_paths.values():
            self.assertTrue(os.path.exists(path))
    
    def test_target_size_initialization(self):
        """测试指定目标尺寸的初始化"""
        target_size = (1024, 768)
        processor = ImageProcessor(target_size=target_size)
        
        images = processor.load_cube_images(self.test_images)
        normalized = processor.normalize_images(images)
        
        # 检查所有图片是否为指定尺寸
        for image in normalized.values():
            self.assertEqual(image.shape[:2], target_size[::-1])


class TestImageProcessorEdgeCases(unittest.TestCase):
    """图像处理器边界情况测试"""
    
    def setUp(self):
        self.processor = ImageProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_empty_image_dict(self):
        """测试空图片字典"""
        result = self.processor.process_cube_images({})
        self.assertEqual(len(result), 0)
    
    def test_missing_faces(self):
        """测试缺少某些面的图片"""
        # 只提供部分面的图片
        partial_images = {
            'front': self._create_test_image('front'),
            'back': self._create_test_image('back')
        }
        
        result = self.processor.process_cube_images(partial_images)
        self.assertEqual(len(result), 2)
    
    def test_invalid_image_format(self):
        """测试无效图片格式"""
        # 创建文本文件而不是图片
        invalid_path = os.path.join(self.temp_dir, "invalid.txt")
        with open(invalid_path, 'w') as f:
            f.write("not an image")
        
        image = self.processor.load_image(invalid_path)
        self.assertIsNone(image)
    
    def _create_test_image(self, name):
        """创建测试图片"""
        image = np.random.randint(0, 255, (256, 256, 3), dtype=np.uint8)
        image_path = os.path.join(self.temp_dir, f"{name}.png")
        cv2.imwrite(image_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
        return image_path


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)

"""
3D长方体可视化器主接口
整合所有模块，提供简单易用的API
"""

import numpy as np
import open3d as o3d
from typing import Dict, Union, Tuple, Optional
from pathlib import Path
import logging

# 导入项目模块
from image_processor import ImageProcessor
from geometry_builder import GeometryBuilder
from texture_mapper import TextureMapper
from viewer_3d import Interactive3DViewer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Cube3DVisualizer:
    """3D长方体可视化器主类"""
    
    def __init__(self, dimensions: Tuple[float, float, float] = (2.0, 2.0, 2.0),
                 target_image_size: Optional[Tuple[int, int]] = None):
        """
        初始化可视化器
        
        Args:
            dimensions: 长方体尺寸 (width, height, depth)
            target_image_size: 目标图片尺寸 (width, height)
        """
        self.dimensions = dimensions
        
        # 初始化各个模块
        self.image_processor = ImageProcessor(target_size=target_image_size)
        self.geometry_builder = GeometryBuilder(dimensions)
        self.texture_mapper = TextureMapper()
        
        # 存储处理结果
        self.processed_images = None
        self.mesh = None
        self.wireframe = None
        
        logger.info(f"3D可视化器已初始化，尺寸: {dimensions}")
    
    def load_and_process_images(self, image_paths: Dict[str, Union[str, Path]], 
                              enhance: bool = False) -> Dict[str, np.ndarray]:
        """
        加载和处理图片
        
        Args:
            image_paths: 图片路径字典，键为面名称
            enhance: 是否进行图像增强
            
        Returns:
            处理后的图片字典
        """
        self.processed_images = self.image_processor.process_cube_images(
            image_paths, enhance=enhance)
        
        if not self.processed_images:
            raise ValueError("没有成功处理任何图片")
        
        logger.info(f"成功处理 {len(self.processed_images)} 张图片")
        return self.processed_images
    
    def create_3d_model(self, use_atlas: bool = False) -> o3d.geometry.TriangleMesh:
        """
        创建3D模型
        
        Args:
            use_atlas: 是否使用纹理图集
            
        Returns:
            带纹理的3D网格
        """
        if self.processed_images is None:
            raise ValueError("请先加载和处理图片")
        
        # 创建基础网格
        self.mesh = self.geometry_builder.create_mesh()
        
        # 应用纹理
        self.mesh = self.texture_mapper.apply_texture_to_mesh(
            self.mesh, self.processed_images, use_atlas=use_atlas)
        
        # 创建线框
        self.wireframe = self.geometry_builder.create_wireframe()
        
        logger.info("3D模型创建完成")
        return self.mesh
    
    def display_interactive(self, window_name: str = "3D长方体查看器",
                          window_size: Tuple[int, int] = (1024, 768)) -> None:
        """
        显示交互式3D查看器
        
        Args:
            window_name: 窗口名称
            window_size: 窗口尺寸 (width, height)
        """
        if self.mesh is None:
            raise ValueError("请先创建3D模型")
        
        # 创建查看器
        viewer = Interactive3DViewer(
            window_name=window_name,
            window_width=window_size[0],
            window_height=window_size[1]
        )
        
        # 添加几何体
        viewer.add_cube_mesh(self.mesh, self.wireframe)
        
        # 运行查看器
        viewer.run()
    
    def save_model(self, output_path: Union[str, Path], 
                  format: str = "obj") -> bool:
        """
        保存3D模型
        
        Args:
            output_path: 输出路径
            format: 输出格式 ("obj", "ply", "stl")
            
        Returns:
            是否成功保存
        """
        if self.mesh is None:
            raise ValueError("请先创建3D模型")
        
        try:
            output_path = Path(output_path)
            
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存模型
            if format.lower() == "obj":
                o3d.io.write_triangle_mesh(str(output_path), self.mesh, 
                                         write_triangle_uvs=True)
            elif format.lower() == "ply":
                o3d.io.write_triangle_mesh(str(output_path), self.mesh)
            elif format.lower() == "stl":
                o3d.io.write_triangle_mesh(str(output_path), self.mesh)
            else:
                logger.error(f"不支持的格式: {format}")
                return False
            
            logger.info(f"模型已保存: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            return False
    
    def create_and_display(self, image_paths: Dict[str, Union[str, Path]],
                         enhance: bool = False,
                         use_atlas: bool = False,
                         display: bool = True,
                         save_path: Optional[Union[str, Path]] = None,
                         save_format: str = "obj") -> o3d.geometry.TriangleMesh:
        """
        一键创建和显示3D模型（主要接口）
        
        Args:
            image_paths: 图片路径字典
            enhance: 是否进行图像增强
            use_atlas: 是否使用纹理图集
            display: 是否显示模型
            save_path: 保存路径（可选）
            save_format: 保存格式
            
        Returns:
            创建的3D网格
        """
        # 加载和处理图片
        self.load_and_process_images(image_paths, enhance=enhance)
        
        # 创建3D模型
        mesh = self.create_3d_model(use_atlas=use_atlas)
        
        # 保存模型（如果指定）
        if save_path:
            self.save_model(save_path, save_format)
        
        # 显示模型（如果需要）
        if display:
            self.display_interactive()
        
        return mesh
    
    def get_model_info(self) -> Dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.mesh is None:
            return {"error": "模型未创建"}
        
        vertices = np.asarray(self.mesh.vertices)
        triangles = np.asarray(self.mesh.triangles)
        
        info = {
            "dimensions": {
                "width": self.dimensions[0],
                "height": self.dimensions[1],
                "depth": self.dimensions[2]
            },
            "mesh_stats": {
                "num_vertices": len(vertices),
                "num_triangles": len(triangles),
                "has_vertex_colors": self.mesh.has_vertex_colors(),
                "has_triangle_uvs": self.mesh.has_triangle_uvs()
            },
            "image_stats": {
                "num_processed_images": len(self.processed_images) if self.processed_images else 0,
                "image_faces": list(self.processed_images.keys()) if self.processed_images else []
            }
        }
        
        # 添加面信息
        if hasattr(self.geometry_builder, 'get_face_info'):
            face_info = self.geometry_builder.get_face_info()
            info["face_info"] = {}
            for face_name, face_data in face_info.items():
                info["face_info"][face_name] = {
                    "center": face_data["center"].tolist(),
                    "normal": face_data["normal"].tolist()
                }
        
        return info
    
    def export_images(self, output_dir: Union[str, Path]) -> Dict[str, str]:
        """
        导出处理后的图片
        
        Args:
            output_dir: 输出目录
            
        Returns:
            导出的文件路径字典
        """
        if self.processed_images is None:
            raise ValueError("没有处理后的图片可导出")
        
        return self.image_processor.save_processed_images(
            self.processed_images, output_dir)
    
    def create_texture_atlas(self, output_path: Union[str, Path]) -> bool:
        """
        创建并保存纹理图集
        
        Args:
            output_path: 输出路径
            
        Returns:
            是否成功创建
        """
        if self.processed_images is None:
            raise ValueError("没有处理后的图片可创建图集")
        
        try:
            atlas, uv_regions = self.texture_mapper.create_texture_atlas(
                self.processed_images)
            
            self.texture_mapper.save_texture_atlas(atlas, str(output_path))
            
            # 保存UV区域信息
            import json
            uv_info_path = str(output_path).replace('.png', '_uv_info.json')
            with open(uv_info_path, 'w', encoding='utf-8') as f:
                json.dump(uv_regions, f, indent=2)
            
            logger.info(f"纹理图集已保存: {output_path}")
            logger.info(f"UV信息已保存: {uv_info_path}")
            return True
        except Exception as e:
            logger.error(f"创建纹理图集失败: {e}")
            return False


def create_demo_images(output_dir: Union[str, Path]) -> Dict[str, str]:
    """
    创建演示用的彩色图片
    
    Args:
        output_dir: 输出目录
        
    Returns:
        创建的图片路径字典
    """
    import cv2
    
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义每个面的颜色
    face_colors = {
        'front': [255, 100, 100],   # 红色
        'back': [100, 255, 100],    # 绿色
        'left': [100, 100, 255],    # 蓝色
        'right': [255, 255, 100],   # 黄色
        'top': [255, 100, 255],     # 紫色
    }
    
    image_paths = {}
    
    for face, color in face_colors.items():
        # 创建纯色图片
        image = np.full((512, 512, 3), color, dtype=np.uint8)
        
        # 添加文字标识
        cv2.putText(image, face.upper(), (200, 256), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 保存图片
        image_path = output_dir / f"{face}.png"
        cv2.imwrite(str(image_path), image)
        image_paths[face] = str(image_path)
    
    logger.info(f"演示图片已创建在: {output_dir}")
    return image_paths


if __name__ == "__main__":
    # 演示代码
    print("3D长方体可视化器演示")
    
    # 创建演示图片
    demo_images = create_demo_images("images/demo")
    
    # 创建可视化器
    visualizer = Cube3DVisualizer(dimensions=(3.0, 2.0, 1.5))
    
    # 创建和显示3D模型
    mesh = visualizer.create_and_display(
        image_paths=demo_images,
        enhance=False,
        use_atlas=False,
        display=True,
        save_path="output/demo_cube.obj"
    )
    
    # 打印模型信息
    info = visualizer.get_model_info()
    print("\n模型信息:")
    print(f"尺寸: {info['dimensions']}")
    print(f"顶点数: {info['mesh_stats']['num_vertices']}")
    print(f"三角形数: {info['mesh_stats']['num_triangles']}")
    print(f"处理的图片数: {info['image_stats']['num_processed_images']}")

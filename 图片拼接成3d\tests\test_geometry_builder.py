"""
几何建模模块测试
"""

import unittest
import numpy as np
import open3d as o3d
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

from geometry_builder import GeometryBuilder


class TestGeometryBuilder(unittest.TestCase):
    """几何建模器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.builder = GeometryBuilder(dimensions=(2.0, 1.5, 1.0))
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.builder.width, 2.0)
        self.assertEqual(self.builder.height, 1.5)
        self.assertEqual(self.builder.depth, 1.0)
    
    def test_create_box_vertices(self):
        """测试创建长方体顶点"""
        vertices = self.builder.create_box_vertices()
        
        # 检查顶点数量
        self.assertEqual(len(vertices), 8)
        
        # 检查顶点形状
        self.assertEqual(vertices.shape, (8, 3))
        
        # 检查顶点坐标范围
        w, h, d = 1.0, 0.75, 0.5  # 半尺寸
        
        # X坐标应该在[-w, w]范围内
        self.assertTrue(np.all(vertices[:, 0] >= -w))
        self.assertTrue(np.all(vertices[:, 0] <= w))
        
        # Y坐标应该在[-h, h]范围内
        self.assertTrue(np.all(vertices[:, 1] >= -h))
        self.assertTrue(np.all(vertices[:, 1] <= h))
        
        # Z坐标应该在[-d, d]范围内
        self.assertTrue(np.all(vertices[:, 2] >= -d))
        self.assertTrue(np.all(vertices[:, 2] <= d))
    
    def test_create_box_faces(self):
        """测试创建长方体面"""
        faces = self.builder.create_box_faces()
        
        # 检查面数量（12个三角形）
        self.assertEqual(len(faces), 12)
        
        # 检查面形状
        self.assertEqual(faces.shape, (12, 3))
        
        # 检查顶点索引范围
        self.assertTrue(np.all(faces >= 0))
        self.assertTrue(np.all(faces < 8))
    
    def test_create_uv_coordinates(self):
        """测试创建UV坐标"""
        uv_coords = self.builder.create_uv_coordinates()
        
        # 检查面数量
        self.assertEqual(len(uv_coords), 6)
        
        # 检查每个面的UV坐标
        expected_faces = ['front', 'back', 'left', 'right', 'bottom', 'top']
        for face in expected_faces:
            self.assertIn(face, uv_coords)
            
            # 检查UV坐标形状
            uv = uv_coords[face]
            self.assertEqual(uv.shape, (4, 2))
            
            # 检查UV坐标范围（应该在0-1之间）
            self.assertTrue(np.all(uv >= 0.0))
            self.assertTrue(np.all(uv <= 1.0))
    
    def test_get_face_vertices(self):
        """测试获取面顶点"""
        # 先创建顶点和UV坐标
        self.builder.create_box_vertices()
        self.builder.create_uv_coordinates()
        
        # 测试每个面
        face_names = ['front', 'back', 'left', 'right', 'top', 'bottom']
        
        for face_name in face_names:
            vertices, uv = self.builder.get_face_vertices(face_name)
            
            # 检查顶点数量
            self.assertEqual(len(vertices), 4)
            self.assertEqual(vertices.shape, (4, 3))
            
            # 检查UV坐标
            self.assertEqual(len(uv), 4)
            self.assertEqual(uv.shape, (4, 2))
    
    def test_get_face_vertices_invalid_face(self):
        """测试获取无效面名称"""
        with self.assertRaises(ValueError):
            self.builder.get_face_vertices("invalid_face")
    
    def test_create_mesh(self):
        """测试创建网格"""
        mesh = self.builder.create_mesh()
        
        # 检查是否为Open3D网格
        self.assertIsInstance(mesh, o3d.geometry.TriangleMesh)
        
        # 检查顶点和面数量
        vertices = np.asarray(mesh.vertices)
        triangles = np.asarray(mesh.triangles)
        
        self.assertEqual(len(vertices), 8)
        self.assertEqual(len(triangles), 12)
        
        # 检查是否有法向量
        self.assertTrue(mesh.has_vertex_normals())
    
    def test_create_wireframe(self):
        """测试创建线框"""
        wireframe = self.builder.create_wireframe()
        
        # 检查是否为Open3D线集
        self.assertIsInstance(wireframe, o3d.geometry.LineSet)
        
        # 检查顶点和线数量
        points = np.asarray(wireframe.points)
        lines = np.asarray(wireframe.lines)
        
        self.assertEqual(len(points), 8)
        self.assertEqual(len(lines), 12)  # 长方体有12条边
        
        # 检查是否有颜色
        self.assertTrue(wireframe.has_colors())
    
    def test_get_face_info(self):
        """测试获取面信息"""
        face_info = self.builder.get_face_info()
        
        # 检查面数量
        self.assertEqual(len(face_info), 6)
        
        # 检查每个面的信息
        expected_faces = ['front', 'back', 'left', 'right', 'top', 'bottom']
        for face_name in expected_faces:
            self.assertIn(face_name, face_info)
            
            info = face_info[face_name]
            
            # 检查信息内容
            self.assertIn('vertices', info)
            self.assertIn('uv_coordinates', info)
            self.assertIn('center', info)
            self.assertIn('normal', info)
            
            # 检查中心点
            center = info['center']
            self.assertEqual(len(center), 3)
            
            # 检查法向量
            normal = info['normal']
            self.assertEqual(len(normal), 3)
            
            # 法向量应该是单位向量
            norm = np.linalg.norm(normal)
            self.assertAlmostEqual(norm, 1.0, places=5)
    
    def test_face_normal_calculation(self):
        """测试面法向量计算"""
        # 创建一个简单的四边形顶点
        vertices = np.array([
            [0, 0, 0],
            [1, 0, 0],
            [1, 1, 0],
            [0, 1, 0]
        ], dtype=np.float32)
        
        normal = self.builder._calculate_face_normal(vertices)
        
        # 这个面的法向量应该是(0, 0, 1)
        expected_normal = np.array([0, 0, 1])
        np.testing.assert_array_almost_equal(normal, expected_normal, decimal=5)
    
    def test_different_dimensions(self):
        """测试不同尺寸的长方体"""
        dimensions_list = [
            (1.0, 1.0, 1.0),  # 立方体
            (2.0, 1.0, 0.5),  # 扁平长方体
            (0.5, 3.0, 1.0),  # 高长方体
        ]
        
        for dimensions in dimensions_list:
            builder = GeometryBuilder(dimensions)
            
            # 创建网格
            mesh = builder.create_mesh()
            vertices = np.asarray(mesh.vertices)
            
            # 检查顶点坐标范围
            w, h, d = [dim / 2 for dim in dimensions]
            
            self.assertTrue(np.all(vertices[:, 0] >= -w))
            self.assertTrue(np.all(vertices[:, 0] <= w))
            self.assertTrue(np.all(vertices[:, 1] >= -h))
            self.assertTrue(np.all(vertices[:, 1] <= h))
            self.assertTrue(np.all(vertices[:, 2] >= -d))
            self.assertTrue(np.all(vertices[:, 2] <= d))


class TestGeometryBuilderEdgeCases(unittest.TestCase):
    """几何建模器边界情况测试"""
    
    def test_zero_dimensions(self):
        """测试零尺寸"""
        builder = GeometryBuilder(dimensions=(0.0, 1.0, 1.0))
        vertices = builder.create_box_vertices()
        
        # 应该仍然创建8个顶点
        self.assertEqual(len(vertices), 8)
        
        # X坐标应该都是0
        self.assertTrue(np.all(vertices[:, 0] == 0.0))
    
    def test_very_small_dimensions(self):
        """测试非常小的尺寸"""
        builder = GeometryBuilder(dimensions=(0.001, 0.001, 0.001))
        mesh = builder.create_mesh()
        
        # 应该仍然能创建有效的网格
        self.assertIsInstance(mesh, o3d.geometry.TriangleMesh)
        self.assertEqual(len(np.asarray(mesh.vertices)), 8)
    
    def test_very_large_dimensions(self):
        """测试非常大的尺寸"""
        builder = GeometryBuilder(dimensions=(1000.0, 1000.0, 1000.0))
        mesh = builder.create_mesh()
        
        # 应该仍然能创建有效的网格
        self.assertIsInstance(mesh, o3d.geometry.TriangleMesh)
        self.assertEqual(len(np.asarray(mesh.vertices)), 8)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)

"""
简化的3D几何建模模块
不依赖Open3D，仅提供基础几何计算
"""

import numpy as np
from typing import Tuple, List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleGeometryBuilder:
    """简化的3D几何建模器类"""
    
    def __init__(self, dimensions: Tuple[float, float, float] = (2.0, 2.0, 2.0)):
        """
        初始化几何建模器
        
        Args:
            dimensions: 长方体尺寸 (width, height, depth)
        """
        self.width, self.height, self.depth = dimensions
        self.vertices = None
        self.faces = None
        self.uv_coordinates = None
        
    def create_box_vertices(self) -> np.ndarray:
        """
        创建长方体的8个顶点
        
        Returns:
            顶点坐标数组 (8, 3)
        """
        w, h, d = self.width / 2, self.height / 2, self.depth / 2
        
        # 定义8个顶点坐标（以原点为中心）
        vertices = np.array([
            # 前面4个顶点 (z = d)
            [-w, -h,  d],  # 0: 左下前
            [ w, -h,  d],  # 1: 右下前
            [ w,  h,  d],  # 2: 右上前
            [-w,  h,  d],  # 3: 左上前
            
            # 后面4个顶点 (z = -d)
            [-w, -h, -d],  # 4: 左下后
            [ w, -h, -d],  # 5: 右下后
            [ w,  h, -d],  # 6: 右上后
            [-w,  h, -d],  # 7: 左上后
        ], dtype=np.float32)
        
        self.vertices = vertices
        logger.info(f"创建了 {len(vertices)} 个顶点")
        return vertices
    
    def create_box_faces(self) -> np.ndarray:
        """
        创建长方体的6个面（每个面由2个三角形组成）
        
        Returns:
            面索引数组 (12, 3) - 12个三角形
        """
        # 定义6个面，每个面用2个三角形表示
        faces = np.array([
            # 前面 (z = depth/2)
            [0, 1, 2], [0, 2, 3],
            
            # 后面 (z = -depth/2)  
            [4, 7, 6], [4, 6, 5],
            
            # 左面 (x = -width/2)
            [4, 0, 3], [4, 3, 7],
            
            # 右面 (x = width/2)
            [1, 5, 6], [1, 6, 2],
            
            # 底面 (y = -height/2)
            [4, 5, 1], [4, 1, 0],
            
            # 顶面 (y = height/2)
            [3, 2, 6], [3, 6, 7],
        ], dtype=np.int32)
        
        self.faces = faces
        logger.info(f"创建了 {len(faces)} 个三角形面")
        return faces
    
    def create_uv_coordinates(self) -> Dict[str, np.ndarray]:
        """
        为每个面创建UV纹理坐标
        
        Returns:
            每个面的UV坐标字典
        """
        # 标准的UV坐标（0,0到1,1）
        uv_quad = np.array([
            [0.0, 0.0],  # 左下
            [1.0, 0.0],  # 右下
            [1.0, 1.0],  # 右上
            [0.0, 1.0],  # 左上
        ], dtype=np.float32)
        
        # 为每个面分配UV坐标
        uv_coordinates = {
            'front': uv_quad.copy(),
            'back': uv_quad.copy(),
            'left': uv_quad.copy(),
            'right': uv_quad.copy(),
            'bottom': uv_quad.copy(),
            'top': uv_quad.copy(),
        }
        
        self.uv_coordinates = uv_coordinates
        logger.info("创建了UV纹理坐标")
        return uv_coordinates
    
    def get_face_vertices(self, face_name: str) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取指定面的顶点坐标和UV坐标
        
        Args:
            face_name: 面名称 ('front', 'back', 'left', 'right', 'top', 'bottom')
            
        Returns:
            (顶点坐标, UV坐标)
        """
        if self.vertices is None:
            self.create_box_vertices()
        if self.uv_coordinates is None:
            self.create_uv_coordinates()
        
        # 定义每个面对应的顶点索引
        face_vertex_indices = {
            'front': [0, 1, 2, 3],    # 前面
            'back': [4, 7, 6, 5],     # 后面（注意顺序）
            'left': [4, 0, 3, 7],     # 左面
            'right': [1, 5, 6, 2],    # 右面
            'bottom': [4, 5, 1, 0],   # 底面
            'top': [3, 2, 6, 7],      # 顶面
        }
        
        if face_name not in face_vertex_indices:
            raise ValueError(f"未知的面名称: {face_name}")
        
        indices = face_vertex_indices[face_name]
        face_vertices = self.vertices[indices]
        face_uv = self.uv_coordinates[face_name]
        
        return face_vertices, face_uv
    
    def get_face_info(self) -> Dict[str, Dict]:
        """
        获取所有面的详细信息
        
        Returns:
            包含每个面信息的字典
        """
        face_info = {}
        face_names = ['front', 'back', 'left', 'right', 'top', 'bottom']
        
        for face_name in face_names:
            vertices, uv = self.get_face_vertices(face_name)
            face_info[face_name] = {
                'vertices': vertices,
                'uv_coordinates': uv,
                'center': np.mean(vertices, axis=0),
                'normal': self._calculate_face_normal(vertices)
            }
        
        return face_info
    
    def _calculate_face_normal(self, vertices: np.ndarray) -> np.ndarray:
        """
        计算面的法向量
        
        Args:
            vertices: 面的顶点坐标
            
        Returns:
            单位法向量
        """
        # 使用前三个顶点计算法向量
        v1 = vertices[1] - vertices[0]
        v2 = vertices[2] - vertices[0]
        normal = np.cross(v1, v2)
        
        # 归一化
        norm = np.linalg.norm(normal)
        if norm > 0:
            normal = normal / norm
        
        return normal
    
    def get_wireframe_edges(self) -> List[Tuple[int, int]]:
        """
        获取线框的边
        
        Returns:
            边的列表，每条边由两个顶点索引组成
        """
        # 定义长方体的12条边
        edges = [
            # 前面4条边
            (0, 1), (1, 2), (2, 3), (3, 0),
            # 后面4条边
            (4, 5), (5, 6), (6, 7), (7, 4),
            # 连接前后面的4条边
            (0, 4), (1, 5), (2, 6), (3, 7),
        ]
        
        return edges
    
    def save_obj_file(self, filename: str) -> bool:
        """
        保存为OBJ文件
        
        Args:
            filename: 文件名
            
        Returns:
            是否成功保存
        """
        if self.vertices is None:
            self.create_box_vertices()
        if self.faces is None:
            self.create_box_faces()
        
        try:
            with open(filename, 'w') as f:
                # 写入文件头
                f.write("# 3D长方体模型\n")
                f.write("# 由简化几何建模器生成\n\n")
                
                # 写入顶点
                f.write("# 顶点坐标\n")
                for vertex in self.vertices:
                    f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
                
                f.write("\n# 面定义\n")
                # 写入面（OBJ文件索引从1开始）
                for face in self.faces:
                    f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
            
            logger.info(f"OBJ文件已保存: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存OBJ文件失败: {e}")
            return False
    
    def get_model_info(self) -> Dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        if self.vertices is None:
            self.create_box_vertices()
        if self.faces is None:
            self.create_box_faces()
        
        info = {
            "dimensions": {
                "width": self.width,
                "height": self.height,
                "depth": self.depth
            },
            "mesh_stats": {
                "num_vertices": len(self.vertices),
                "num_triangles": len(self.faces),
                "num_edges": 12
            },
            "bounding_box": {
                "min": self.vertices.min(axis=0).tolist(),
                "max": self.vertices.max(axis=0).tolist(),
                "center": self.vertices.mean(axis=0).tolist()
            }
        }
        
        return info


# 为了兼容性，创建一个别名
GeometryBuilder = SimpleGeometryBuilder


if __name__ == "__main__":
    # 测试代码
    builder = SimpleGeometryBuilder(dimensions=(2.0, 1.5, 1.0))
    
    # 创建基本几何体
    vertices = builder.create_box_vertices()
    faces = builder.create_box_faces()
    uv_coords = builder.create_uv_coordinates()
    
    print(f"顶点数: {len(vertices)}")
    print(f"三角形数: {len(faces)}")
    print(f"UV坐标组数: {len(uv_coords)}")
    
    # 获取面信息
    face_info = builder.get_face_info()
    for face_name, info in face_info.items():
        print(f"{face_name}面中心: {info['center']}")
        print(f"{face_name}面法向量: {info['normal']}")
    
    # 保存OBJ文件
    builder.save_obj_file("test_cube.obj")
    
    # 获取模型信息
    model_info = builder.get_model_info()
    print("\n模型信息:")
    print(f"尺寸: {model_info['dimensions']}")
    print(f"顶点数: {model_info['mesh_stats']['num_vertices']}")
    print(f"边界框: {model_info['bounding_box']}")

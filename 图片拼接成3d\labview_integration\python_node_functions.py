"""
Python Node函数集合
专门为LabVIEW Python Node设计的函数接口
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.append(str(src_dir))

try:
    from image_processor import ImageProcessor
    from geometry_builder_simple import SimpleGeometryBuilder
except ImportError as e:
    print(f"导入模块失败: {e}")


def process_cube_simple(front_path, back_path, left_path, right_path, top_path, 
                       output_path, width=2.0, height=2.0, depth=2.0):
    """
    简单的立方体处理函数
    
    Args:
        front_path: 前面图片路径
        back_path: 后面图片路径
        left_path: 左面图片路径
        right_path: 右面图片路径
        top_path: 上面图片路径
        output_path: 输出OBJ文件路径
        width: 宽度
        height: 高度
        depth: 深度
    
    Returns:
        (success, message): 成功标志和消息
    """
    try:
        # 图片路径字典
        image_paths = {
            'front': front_path,
            'back': back_path,
            'left': left_path,
            'right': right_path,
            'top': top_path
        }
        
        # 检查文件是否存在
        for face, path in image_paths.items():
            if not os.path.exists(path):
                return False, f"{face}面图片不存在: {path}"
        
        # 处理图片
        processor = ImageProcessor()
        processed_images = processor.process_cube_images(image_paths, enhance=False)
        
        if not processed_images:
            return False, "图片处理失败"
        
        # 创建几何体
        builder = SimpleGeometryBuilder(dimensions=(width, height, depth))
        
        # 保存模型
        success = builder.save_obj_file(output_path)
        
        if success:
            return True, f"模型已保存到: {output_path}"
        else:
            return False, "模型保存失败"
            
    except Exception as e:
        return False, f"处理错误: {str(e)}"


def process_cube_enhanced(front_path, back_path, left_path, right_path, top_path, 
                         output_path, width=2.0, height=2.0, depth=2.0, enhance=True):
    """
    增强版立方体处理函数
    
    Args:
        front_path: 前面图片路径
        back_path: 后面图片路径
        left_path: 左面图片路径
        right_path: 右面图片路径
        top_path: 上面图片路径
        output_path: 输出OBJ文件路径
        width: 宽度
        height: 高度
        depth: 深度
        enhance: 是否进行图像增强
    
    Returns:
        (success, message, info): 成功标志、消息和模型信息
    """
    try:
        # 图片路径字典
        image_paths = {
            'front': front_path,
            'back': back_path,
            'left': left_path,
            'right': right_path,
            'top': top_path
        }
        
        # 检查文件是否存在
        for face, path in image_paths.items():
            if not os.path.exists(path):
                return False, f"{face}面图片不存在: {path}", {}
        
        # 处理图片
        processor = ImageProcessor()
        processed_images = processor.process_cube_images(image_paths, enhance=enhance)
        
        if not processed_images:
            return False, "图片处理失败", {}
        
        # 创建几何体
        builder = SimpleGeometryBuilder(dimensions=(width, height, depth))
        vertices = builder.create_box_vertices()
        faces = builder.create_box_faces()
        
        # 保存模型
        success = builder.save_obj_file(output_path)
        
        if success:
            # 获取模型信息
            model_info = builder.get_model_info()
            
            return True, f"模型已保存到: {output_path}", model_info
        else:
            return False, "模型保存失败", {}
            
    except Exception as e:
        return False, f"处理错误: {str(e)}", {}


def validate_image_paths(front_path, back_path, left_path, right_path, top_path):
    """
    验证图片路径
    
    Args:
        front_path: 前面图片路径
        back_path: 后面图片路径
        left_path: 左面图片路径
        right_path: 右面图片路径
        top_path: 上面图片路径
    
    Returns:
        (valid, missing_files): 验证结果和缺失文件列表
    """
    image_paths = {
        'front': front_path,
        'back': back_path,
        'left': left_path,
        'right': right_path,
        'top': top_path
    }
    
    missing_files = []
    
    for face, path in image_paths.items():
        if not os.path.exists(path):
            missing_files.append(f"{face}: {path}")
    
    return len(missing_files) == 0, missing_files


def get_image_info(image_path):
    """
    获取图片信息
    
    Args:
        image_path: 图片路径
    
    Returns:
        (success, info): 成功标志和图片信息
    """
    try:
        if not os.path.exists(image_path):
            return False, {"error": "文件不存在"}
        
        processor = ImageProcessor()
        image = processor.load_image(image_path)
        
        if image is None:
            return False, {"error": "无法读取图片"}
        
        info = {
            "width": image.shape[1],
            "height": image.shape[0],
            "channels": image.shape[2] if len(image.shape) > 2 else 1,
            "size_mb": os.path.getsize(image_path) / (1024 * 1024)
        }
        
        return True, info
        
    except Exception as e:
        return False, {"error": str(e)}


def create_test_images(output_dir, size=512):
    """
    创建测试图片
    
    Args:
        output_dir: 输出目录
        size: 图片尺寸
    
    Returns:
        (success, image_paths): 成功标志和图片路径字典
    """
    try:
        import cv2
        import numpy as np
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 定义每个面的颜色
        face_colors = {
            'front': [255, 100, 100],   # 红色
            'back': [100, 255, 100],    # 绿色
            'left': [100, 100, 255],    # 蓝色
            'right': [255, 255, 100],   # 黄色
            'top': [255, 100, 255],     # 紫色
        }
        
        image_paths = {}
        
        for face, color in face_colors.items():
            # 创建图片
            image = np.full((size, size, 3), color, dtype=np.uint8)
            
            # 添加文字
            cv2.putText(image, face.upper(), (size//4, size//2), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
            
            # 保存图片
            image_path = output_path / f"{face}.png"
            cv2.imwrite(str(image_path), image)
            image_paths[face] = str(image_path)
        
        return True, image_paths
        
    except Exception as e:
        return False, {"error": str(e)}


def batch_process_cubes(config_list):
    """
    批量处理立方体
    
    Args:
        config_list: 配置列表，每个配置包含图片路径和输出路径
    
    Returns:
        (success_count, results): 成功数量和详细结果
    """
    results = []
    success_count = 0
    
    for i, config in enumerate(config_list):
        try:
            # 提取配置
            image_paths = config.get('image_paths', {})
            output_path = config.get('output_path', f'output_{i}.obj')
            dimensions = config.get('dimensions', [2.0, 2.0, 2.0])
            enhance = config.get('enhance', False)
            
            # 处理
            success, message, info = process_cube_enhanced(
                image_paths.get('front', ''),
                image_paths.get('back', ''),
                image_paths.get('left', ''),
                image_paths.get('right', ''),
                image_paths.get('top', ''),
                output_path,
                dimensions[0], dimensions[1], dimensions[2],
                enhance
            )
            
            result = {
                'index': i,
                'success': success,
                'message': message,
                'output_path': output_path if success else None,
                'info': info if success else None
            }
            
            results.append(result)
            
            if success:
                success_count += 1
                
        except Exception as e:
            results.append({
                'index': i,
                'success': False,
                'message': f"配置错误: {str(e)}",
                'output_path': None,
                'info': None
            })
    
    return success_count, results


# 为了兼容性，提供简化的函数名
process_cube = process_cube_simple
validate_paths = validate_image_paths
get_info = get_image_info

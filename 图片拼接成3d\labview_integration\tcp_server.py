"""
TCP服务器 - 用于LabVIEW TCP通信
提供高性能的网络接口，支持并发处理
"""

import socket
import json
import threading
import time
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from image_processor import ImageProcessor
    from geometry_builder_simple import SimpleGeometryBuilder
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class CubeVisualizerServer:
    """3D立方体可视化TCP服务器"""
    
    def __init__(self, host='localhost', port=8888):
        """
        初始化服务器
        
        Args:
            host: 服务器地址
            port: 服务器端口
        """
        self.host = host
        self.port = port
        self.running = False
        self.server_socket = None
        
        # 初始化处理器
        self.image_processor = ImageProcessor()
        
        print(f"3D立方体可视化服务器初始化完成")
        print(f"服务器地址: {host}:{port}")
    
    def start_server(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            print(f"服务器已启动，监听 {self.host}:{self.port}")
            print("等待客户端连接...")
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    print(f"客户端连接: {client_address}")
                    
                    # 为每个客户端创建新线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        print(f"接受连接时发生错误: {e}")
                    break
                    
        except Exception as e:
            print(f"启动服务器失败: {e}")
        finally:
            self.stop_server()
    
    def handle_client(self, client_socket, client_address):
        """处理客户端请求"""
        try:
            # 接收请求数据
            data = b''
            while True:
                chunk = client_socket.recv(4096)
                if not chunk:
                    break
                data += chunk
                
                # 检查是否接收完整JSON
                try:
                    json.loads(data.decode('utf-8'))
                    break
                except json.JSONDecodeError:
                    continue
            
            if not data:
                print(f"客户端 {client_address} 断开连接")
                return
            
            # 解析请求
            try:
                request = json.loads(data.decode('utf-8'))
                print(f"收到请求: {request.get('command', 'unknown')}")
            except json.JSONDecodeError as e:
                response = self._create_error_response(f"JSON解析错误: {e}")
                self._send_response(client_socket, response)
                return
            
            # 处理请求
            response = self.process_request(request)
            
            # 发送响应
            self._send_response(client_socket, response)
            
        except Exception as e:
            print(f"处理客户端 {client_address} 时发生错误: {e}")
            error_response = self._create_error_response(f"服务器内部错误: {e}")
            self._send_response(client_socket, error_response)
        finally:
            client_socket.close()
            print(f"客户端 {client_address} 连接已关闭")
    
    def process_request(self, request):
        """处理具体请求"""
        command = request.get('command', '')
        
        if command == 'process_cube':
            return self._process_cube_request(request)
        elif command == 'validate_images':
            return self._validate_images_request(request)
        elif command == 'get_server_info':
            return self._get_server_info()
        elif command == 'ping':
            return {'success': True, 'message': 'pong', 'timestamp': time.time()}
        else:
            return self._create_error_response(f"未知命令: {command}")
    
    def _process_cube_request(self, request):
        """处理立方体生成请求"""
        try:
            # 提取参数
            image_paths = request.get('image_paths', {})
            output_path = request.get('output_path', 'output.obj')
            dimensions = request.get('dimensions', [2.0, 2.0, 2.0])
            enhance = request.get('enhance', False)
            
            # 验证必需参数
            required_faces = ['front', 'back', 'left', 'right', 'top']
            for face in required_faces:
                if face not in image_paths:
                    return self._create_error_response(f"缺少{face}面图片路径")
            
            # 处理图片
            processed_images = self.image_processor.process_cube_images(
                image_paths, enhance=enhance
            )
            
            if not processed_images:
                return self._create_error_response("图片处理失败")
            
            # 创建几何体
            builder = SimpleGeometryBuilder(
                dimensions=(dimensions[0], dimensions[1], dimensions[2])
            )
            vertices = builder.create_box_vertices()
            faces = builder.create_box_faces()
            
            # 保存模型
            success = builder.save_obj_file(output_path)
            
            if not success:
                return self._create_error_response("模型保存失败")
            
            # 创建成功响应
            model_info = builder.get_model_info()
            
            return {
                'success': True,
                'message': '立方体处理完成',
                'output_path': output_path,
                'model_info': model_info,
                'processed_images': list(processed_images.keys()),
                'timestamp': time.time()
            }
            
        except Exception as e:
            return self._create_error_response(f"处理立方体时发生错误: {e}")
    
    def _validate_images_request(self, request):
        """验证图片路径请求"""
        try:
            image_paths = request.get('image_paths', {})
            
            results = {}
            all_valid = True
            
            for face, path in image_paths.items():
                if Path(path).exists():
                    # 尝试加载图片获取信息
                    image = self.image_processor.load_image(path)
                    if image is not None:
                        results[face] = {
                            'valid': True,
                            'width': image.shape[1],
                            'height': image.shape[0],
                            'channels': image.shape[2] if len(image.shape) > 2 else 1
                        }
                    else:
                        results[face] = {'valid': False, 'error': '无法读取图片'}
                        all_valid = False
                else:
                    results[face] = {'valid': False, 'error': '文件不存在'}
                    all_valid = False
            
            return {
                'success': True,
                'all_valid': all_valid,
                'results': results,
                'timestamp': time.time()
            }
            
        except Exception as e:
            return self._create_error_response(f"验证图片时发生错误: {e}")
    
    def _get_server_info(self):
        """获取服务器信息"""
        return {
            'success': True,
            'server_info': {
                'host': self.host,
                'port': self.port,
                'running': self.running,
                'supported_commands': [
                    'process_cube',
                    'validate_images', 
                    'get_server_info',
                    'ping'
                ]
            },
            'timestamp': time.time()
        }
    
    def _create_error_response(self, error_message):
        """创建错误响应"""
        return {
            'success': False,
            'error': error_message,
            'timestamp': time.time()
        }
    
    def _send_response(self, client_socket, response):
        """发送响应"""
        try:
            response_json = json.dumps(response, ensure_ascii=False)
            response_bytes = response_json.encode('utf-8')
            client_socket.send(response_bytes)
        except Exception as e:
            print(f"发送响应失败: {e}")
    
    def stop_server(self):
        """停止服务器"""
        self.running = False
        if self.server_socket:
            self.server_socket.close()
        print("服务器已停止")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="3D立方体可视化TCP服务器")
    parser.add_argument("--host", default="localhost", help="服务器地址")
    parser.add_argument("--port", type=int, default=8888, help="服务器端口")
    
    args = parser.parse_args()
    
    # 创建并启动服务器
    server = CubeVisualizerServer(host=args.host, port=args.port)
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务器...")
        server.stop_server()


if __name__ == "__main__":
    main()

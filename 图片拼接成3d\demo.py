"""
简单演示脚本
测试3D可视化系统的基本功能
"""

import sys
import os
import numpy as np
import cv2
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from image_processor import ImageProcessor
    from geometry_builder import GeometryBuilder
    print("✓ 成功导入核心模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)


def create_demo_images():
    """创建演示图片"""
    print("创建演示图片...")
    
    # 创建images目录
    images_dir = Path("images/demo")
    images_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义每个面的颜色
    face_colors = {
        'front': [255, 100, 100],   # 红色
        'back': [100, 255, 100],    # 绿色
        'left': [100, 100, 255],    # 蓝色
        'right': [255, 255, 100],   # 黄色
        'top': [255, 100, 255],     # 紫色
    }
    
    image_paths = {}
    
    for face, color in face_colors.items():
        # 创建512x512的纯色图片
        image = np.full((512, 512, 3), color, dtype=np.uint8)
        
        # 添加文字标识
        cv2.putText(image, face.upper(), (150, 256), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 保存图片
        image_path = images_dir / f"{face}.png"
        cv2.imwrite(str(image_path), image)
        image_paths[face] = str(image_path)
        print(f"  ✓ 创建 {face} 面图片: {image_path}")
    
    return image_paths


def test_image_processing():
    """测试图像处理"""
    print("\n测试图像处理模块...")
    
    # 创建演示图片
    image_paths = create_demo_images()
    
    # 创建图像处理器
    processor = ImageProcessor()
    
    # 处理图片
    processed_images = processor.process_cube_images(image_paths)
    
    if processed_images:
        print(f"  ✓ 成功处理 {len(processed_images)} 张图片")
        
        # 检查图片尺寸
        for face, image in processed_images.items():
            print(f"    {face}: {image.shape}")
        
        return processed_images
    else:
        print("  ✗ 图片处理失败")
        return None


def test_geometry_building():
    """测试几何建模"""
    print("\n测试几何建模模块...")
    
    # 创建几何建模器
    builder = GeometryBuilder(dimensions=(2.0, 1.5, 1.0))
    
    # 创建顶点
    vertices = builder.create_box_vertices()
    print(f"  ✓ 创建 {len(vertices)} 个顶点")
    
    # 创建面
    faces = builder.create_box_faces()
    print(f"  ✓ 创建 {len(faces)} 个三角形面")
    
    # 创建UV坐标
    uv_coords = builder.create_uv_coordinates()
    print(f"  ✓ 创建 {len(uv_coords)} 组UV坐标")
    
    # 获取面信息
    face_info = builder.get_face_info()
    print(f"  ✓ 获取 {len(face_info)} 个面的信息")
    
    return builder


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("3D长方体可视化系统 - 基本功能测试")
    print("=" * 50)
    
    # 测试图像处理
    processed_images = test_image_processing()
    if not processed_images:
        return False
    
    # 测试几何建模
    builder = test_geometry_building()
    if not builder:
        return False
    
    print("\n✓ 基本功能测试通过!")
    return True


def test_command_line_interface():
    """测试命令行接口"""
    print("\n测试命令行接口...")
    
    # 创建演示图片
    image_paths = create_demo_images()
    
    # 构建命令
    cmd_parts = [
        "python", "src/labview_interface.py",
        "--front", image_paths['front'],
        "--back", image_paths['back'],
        "--left", image_paths['left'],
        "--right", image_paths['right'],
        "--top", image_paths['top'],
        "--width", "2.0",
        "--height", "1.5",
        "--depth", "1.0",
        "--output", "output/demo_result.obj",
        "--no-display"  # 不显示3D窗口
    ]
    
    print("命令行调用:")
    print(" ".join(cmd_parts))
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    return True


def main():
    """主函数"""
    print("3D长方体可视化系统演示")
    
    try:
        # 测试基本功能
        if not test_basic_functionality():
            print("✗ 基本功能测试失败")
            return
        
        # 测试命令行接口
        test_command_line_interface()
        
        print("\n" + "=" * 50)
        print("演示完成!")
        print("=" * 50)
        
        print("\n下一步:")
        print("1. 运行完整示例: python examples/basic_example.py")
        print("2. 运行LabVIEW示例: python examples/labview_example.py")
        print("3. 查看安装指南: INSTALL.md")
        print("4. 查看项目文档: README.md")
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

"""
纹理映射系统
负责将5张2D图片映射到长方体的对应面，处理纹理坐标
"""

import numpy as np
import open3d as o3d
from PIL import Image
from typing import Dict, Tuple, Optional, Union
import cv2
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextureMapper:
    """纹理映射器类"""
    
    def __init__(self):
        """初始化纹理映射器"""
        self.face_mapping = {
            'front': 'front',
            'back': 'back', 
            'left': 'left',
            'right': 'right',
            'top': 'top',
            'bottom': 'top'  # 底面使用顶面的纹理（如果没有底面图片）
        }
        
    def create_texture_atlas(self, images: Dict[str, np.ndarray], 
                           atlas_size: Tuple[int, int] = (1024, 1024)) -> Tuple[np.ndarray, Dict[str, Tuple[float, float, float, float]]]:
        """
        创建纹理图集，将多张图片合并到一张大图中
        
        Args:
            images: 图片字典
            atlas_size: 图集尺寸 (width, height)
            
        Returns:
            (纹理图集, UV区域字典)
        """
        atlas_width, atlas_height = atlas_size
        atlas = np.zeros((atlas_height, atlas_width, 3), dtype=np.uint8)
        
        # 计算每个面在图集中的位置
        # 使用十字形布局：
        #     [top]
        # [left][front][right][back]
        #     [bottom]
        
        face_positions = {
            'front': (1, 1),   # 中心位置
            'back': (3, 1),    # 右侧
            'left': (0, 1),    # 左侧
            'right': (2, 1),   # 右侧
            'top': (1, 0),     # 上方
            'bottom': (1, 2),  # 下方（如果有的话）
        }
        
        # 每个面的尺寸（图集被分为4x3的网格）
        face_width = atlas_width // 4
        face_height = atlas_height // 3
        
        uv_regions = {}
        
        for face_name, image in images.items():
            if face_name in face_positions:
                grid_x, grid_y = face_positions[face_name]
                
                # 计算在图集中的像素位置
                start_x = grid_x * face_width
                start_y = grid_y * face_height
                end_x = start_x + face_width
                end_y = start_y + face_height
                
                # 调整图片尺寸以适应网格
                resized_image = cv2.resize(image, (face_width, face_height))
                
                # 将图片放入图集
                atlas[start_y:end_y, start_x:end_x] = resized_image
                
                # 计算UV坐标（归一化到0-1）
                u_min = start_x / atlas_width
                v_min = start_y / atlas_height
                u_max = end_x / atlas_width
                v_max = end_y / atlas_height
                
                uv_regions[face_name] = (u_min, v_min, u_max, v_max)
                
                logger.info(f"{face_name}面已放入图集，UV区域: ({u_min:.3f}, {v_min:.3f}, {u_max:.3f}, {v_max:.3f})")
        
        return atlas, uv_regions
    
    def create_separate_textures(self, images: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """
        为每个面创建单独的纹理
        
        Args:
            images: 图片字典
            
        Returns:
            处理后的纹理字典
        """
        textures = {}
        
        for face_name, image in images.items():
            # 确保图片是RGB格式
            if len(image.shape) == 3 and image.shape[2] == 3:
                textures[face_name] = image.copy()
            else:
                # 转换为RGB
                if len(image.shape) == 2:
                    textures[face_name] = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
                else:
                    textures[face_name] = image[:, :, :3]
            
            logger.info(f"为{face_name}面创建了纹理，尺寸: {textures[face_name].shape}")
        
        return textures
    
    def apply_texture_to_mesh(self, mesh: o3d.geometry.TriangleMesh, 
                            images: Dict[str, np.ndarray],
                            use_atlas: bool = False) -> o3d.geometry.TriangleMesh:
        """
        将纹理应用到网格
        
        Args:
            mesh: 输入网格
            images: 图片字典
            use_atlas: 是否使用纹理图集
            
        Returns:
            带纹理的网格
        """
        if use_atlas:
            return self._apply_atlas_texture(mesh, images)
        else:
            return self._apply_separate_textures(mesh, images)
    
    def _apply_atlas_texture(self, mesh: o3d.geometry.TriangleMesh, 
                           images: Dict[str, np.ndarray]) -> o3d.geometry.TriangleMesh:
        """
        使用纹理图集应用纹理
        
        Args:
            mesh: 输入网格
            images: 图片字典
            
        Returns:
            带纹理的网格
        """
        # 创建纹理图集
        atlas, uv_regions = self.create_texture_atlas(images)
        
        # 创建UV坐标
        triangle_uvs = self._create_atlas_uv_coordinates(uv_regions)
        
        # 应用到网格
        textured_mesh = mesh.copy()
        
        if triangle_uvs is not None:
            textured_mesh.triangle_uvs = o3d.utility.Vector2dVector(triangle_uvs.reshape(-1, 2))
            
            # 将纹理图集转换为PIL图像并保存
            atlas_pil = Image.fromarray(atlas)
            textured_mesh.textures = [atlas_pil]
        
        logger.info("已应用纹理图集到网格")
        return textured_mesh
    
    def _apply_separate_textures(self, mesh: o3d.geometry.TriangleMesh, 
                               images: Dict[str, np.ndarray]) -> o3d.geometry.TriangleMesh:
        """
        使用单独纹理应用到网格（简化版本）
        
        Args:
            mesh: 输入网格
            images: 图片字典
            
        Returns:
            带颜色的网格（Open3D限制，使用顶点颜色代替纹理）
        """
        textured_mesh = mesh.copy()
        
        # 由于Open3D的纹理支持有限，这里使用顶点颜色作为替代
        # 为每个面分配平均颜色
        vertex_colors = self._calculate_face_colors(images)
        
        if vertex_colors is not None:
            textured_mesh.vertex_colors = o3d.utility.Vector3dVector(vertex_colors)
        
        logger.info("已应用顶点颜色到网格")
        return textured_mesh
    
    def _create_atlas_uv_coordinates(self, uv_regions: Dict[str, Tuple[float, float, float, float]]) -> Optional[np.ndarray]:
        """
        为纹理图集创建UV坐标
        
        Args:
            uv_regions: UV区域字典
            
        Returns:
            UV坐标数组
        """
        # 面名称到三角形索引的映射
        face_triangle_mapping = {
            'front': [0, 1],      # 前面：三角形0,1
            'back': [2, 3],       # 后面：三角形2,3
            'left': [4, 5],       # 左面：三角形4,5
            'right': [6, 7],      # 右面：三角形6,7
            'bottom': [8, 9],     # 底面：三角形8,9
            'top': [10, 11],      # 顶面：三角形10,11
        }
        
        triangle_uvs = []
        
        for face_name, triangle_indices in face_triangle_mapping.items():
            if face_name in uv_regions:
                u_min, v_min, u_max, v_max = uv_regions[face_name]
                
                # 定义面的四个UV坐标
                face_uv = np.array([
                    [u_min, v_max],  # 左下
                    [u_max, v_max],  # 右下
                    [u_max, v_min],  # 右上
                    [u_min, v_min],  # 左上
                ], dtype=np.float32)
                
                # 为该面的两个三角形分配UV坐标
                # 第一个三角形: 0,1,2
                triangle_uvs.append([face_uv[0], face_uv[1], face_uv[2]])
                # 第二个三角形: 0,2,3
                triangle_uvs.append([face_uv[0], face_uv[2], face_uv[3]])
            else:
                # 如果没有该面的纹理，使用默认UV坐标
                default_uv = np.array([
                    [0.0, 1.0], [1.0, 1.0], [1.0, 0.0], [0.0, 0.0]
                ], dtype=np.float32)
                
                triangle_uvs.append([default_uv[0], default_uv[1], default_uv[2]])
                triangle_uvs.append([default_uv[0], default_uv[2], default_uv[3]])
        
        if triangle_uvs:
            return np.array(triangle_uvs, dtype=np.float32)
        
        return None
    
    def _calculate_face_colors(self, images: Dict[str, np.ndarray]) -> Optional[np.ndarray]:
        """
        计算每个面的平均颜色，用于顶点着色
        
        Args:
            images: 图片字典
            
        Returns:
            顶点颜色数组
        """
        # 计算每个面的平均颜色
        face_colors = {}
        
        for face_name, image in images.items():
            # 计算图片的平均颜色
            mean_color = np.mean(image.reshape(-1, 3), axis=0) / 255.0  # 归一化到0-1
            face_colors[face_name] = mean_color
        
        # 为8个顶点分配颜色
        # 每个顶点属于3个面，取这3个面颜色的平均值
        vertex_face_mapping = {
            0: ['front', 'left', 'bottom'],    # 左下前
            1: ['front', 'right', 'bottom'],   # 右下前
            2: ['front', 'right', 'top'],      # 右上前
            3: ['front', 'left', 'top'],       # 左上前
            4: ['back', 'left', 'bottom'],     # 左下后
            5: ['back', 'right', 'bottom'],    # 右下后
            6: ['back', 'right', 'top'],       # 右上后
            7: ['back', 'left', 'top'],        # 左上后
        }
        
        vertex_colors = []
        
        for vertex_idx in range(8):
            faces = vertex_face_mapping[vertex_idx]
            colors = []
            
            for face in faces:
                if face in face_colors:
                    colors.append(face_colors[face])
                else:
                    colors.append([0.5, 0.5, 0.5])  # 默认灰色
            
            # 计算平均颜色
            avg_color = np.mean(colors, axis=0)
            vertex_colors.append(avg_color)
        
        return np.array(vertex_colors, dtype=np.float32)
    
    def save_texture_atlas(self, atlas: np.ndarray, output_path: str) -> None:
        """
        保存纹理图集
        
        Args:
            atlas: 纹理图集数组
            output_path: 输出路径
        """
        # 转换为PIL图像并保存
        atlas_pil = Image.fromarray(atlas)
        atlas_pil.save(output_path)
        logger.info(f"纹理图集已保存到: {output_path}")
    
    def create_material_config(self, images: Dict[str, np.ndarray], 
                             output_path: str) -> Dict:
        """
        创建材质配置文件
        
        Args:
            images: 图片字典
            output_path: 输出路径
            
        Returns:
            材质配置字典
        """
        config = {
            'material_name': 'cube_material',
            'faces': {},
            'atlas_info': {}
        }
        
        # 创建纹理图集
        atlas, uv_regions = self.create_texture_atlas(images)
        
        # 保存图集
        atlas_path = output_path.replace('.json', '_atlas.png')
        self.save_texture_atlas(atlas, atlas_path)
        
        # 配置信息
        config['atlas_info'] = {
            'atlas_path': atlas_path,
            'atlas_size': atlas.shape[:2],
            'uv_regions': uv_regions
        }
        
        for face_name in images.keys():
            if face_name in uv_regions:
                config['faces'][face_name] = {
                    'uv_region': uv_regions[face_name],
                    'has_texture': True
                }
        
        # 保存配置文件
        import json
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"材质配置已保存到: {output_path}")
        return config


if __name__ == "__main__":
    # 测试代码
    mapper = TextureMapper()
    
    # 创建测试图片
    test_images = {}
    for face in ['front', 'back', 'left', 'right', 'top']:
        # 创建不同颜色的测试图片
        color = np.random.randint(0, 255, 3)
        test_image = np.full((256, 256, 3), color, dtype=np.uint8)
        test_images[face] = test_image
    
    # 创建纹理图集
    atlas, uv_regions = mapper.create_texture_atlas(test_images)
    print(f"纹理图集尺寸: {atlas.shape}")
    print(f"UV区域数量: {len(uv_regions)}")
    
    # 创建材质配置
    config = mapper.create_material_config(test_images, 'test_material.json')
    print("材质配置创建完成")

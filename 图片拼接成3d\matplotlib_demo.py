"""
使用matplotlib的3D可视化演示
替代Open3D的简化版本
"""

import sys
import os
import numpy as np
import cv2
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from image_processor import ImageProcessor
    from geometry_builder import GeometryBuilder
    from matplotlib_viewer import InteractiveMatplotlibViewer
    print("✓ 成功导入模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)


def create_demo_images():
    """创建演示图片"""
    print("创建演示图片...")
    
    # 创建images目录
    images_dir = Path("images/matplotlib_demo")
    images_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义每个面的颜色
    face_colors = {
        'front': [255, 100, 100],   # 红色
        'back': [100, 255, 100],    # 绿色
        'left': [100, 100, 255],    # 蓝色
        'right': [255, 255, 100],   # 黄色
        'top': [255, 100, 255],     # 紫色
    }
    
    image_paths = {}
    
    for face, color in face_colors.items():
        # 创建512x512的纯色图片
        image = np.full((512, 512, 3), color, dtype=np.uint8)
        
        # 添加文字标识
        cv2.putText(image, face.upper(), (150, 256), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
        
        # 添加边框
        cv2.rectangle(image, (10, 10), (501, 501), (255, 255, 255), 5)
        
        # 保存图片
        image_path = images_dir / f"{face}.png"
        cv2.imwrite(str(image_path), image)
        image_paths[face] = str(image_path)
        print(f"  ✓ 创建 {face} 面图片: {image_path}")
    
    return image_paths


def process_images(image_paths):
    """处理图片"""
    print("\n处理图片...")
    
    # 创建图像处理器
    processor = ImageProcessor()
    
    # 处理图片
    processed_images = processor.process_cube_images(image_paths)
    
    if processed_images:
        print(f"  ✓ 成功处理 {len(processed_images)} 张图片")
        return processed_images
    else:
        print("  ✗ 图片处理失败")
        return None


def create_geometry():
    """创建几何体"""
    print("\n创建几何体...")
    
    # 创建几何建模器
    builder = GeometryBuilder(dimensions=(2.0, 1.5, 1.0))
    
    # 创建顶点
    vertices = builder.create_box_vertices()
    print(f"  ✓ 创建 {len(vertices)} 个顶点")
    
    # 创建面
    faces = builder.create_box_faces()
    print(f"  ✓ 创建 {len(faces)} 个三角形面")
    
    return builder, vertices, faces


def display_3d_model(vertices, images):
    """显示3D模型"""
    print("\n显示3D模型...")
    
    # 创建输出目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建查看器
    viewer = InteractiveMatplotlibViewer(figsize=(15, 10))
    
    # 显示纹理预览
    viewer.display_texture_preview(images)
    plt_filename = output_dir / "texture_preview.png"
    viewer.save_figure(str(plt_filename))
    print(f"  ✓ 纹理预览已保存: {plt_filename}")
    
    # 显示3D模型
    viewer = InteractiveMatplotlibViewer(figsize=(15, 10))
    viewer.display_cube(vertices, images, show_wireframe=True)
    viewer.setup_interactive_controls()
    
    # 保存图形
    plt_filename = output_dir / "matplotlib_3d_model.png"
    viewer.save_figure(str(plt_filename))
    print(f"  ✓ 3D模型图像已保存: {plt_filename}")
    
    print("\n  控制说明:")
    print("  - 方向键: 旋转模型")
    print("  - R键: 重置视角")
    print("  - 鼠标拖拽: 旋转模型")
    
    # 显示
    viewer.show()


def save_obj_file(vertices, faces, output_path):
    """保存OBJ文件"""
    print(f"\n保存OBJ文件: {output_path}")
    
    try:
        with open(output_path, 'w') as f:
            # 写入文件头
            f.write("# 3D长方体模型\n")
            f.write("# 由3D可视化系统生成\n\n")
            
            # 写入顶点
            f.write("# 顶点坐标\n")
            for i, vertex in enumerate(vertices):
                f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
            
            f.write("\n# 面定义\n")
            # 写入面（OBJ文件索引从1开始）
            for i, face in enumerate(faces):
                f.write(f"f {face[0]+1} {face[1]+1} {face[2]+1}\n")
        
        print(f"  ✓ OBJ文件已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"  ✗ 保存OBJ文件失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("3D长方体可视化系统 - Matplotlib版本")
    print("=" * 60)
    
    try:
        # 创建演示图片
        image_paths = create_demo_images()
        
        # 处理图片
        processed_images = process_images(image_paths)
        if not processed_images:
            return
        
        # 创建几何体
        builder, vertices, faces = create_geometry()
        
        # 保存OBJ文件
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        obj_path = output_dir / "matplotlib_demo_cube.obj"
        save_obj_file(vertices, faces, obj_path)
        
        # 显示3D模型
        display_3d_model(vertices, processed_images)
        
        print("\n" + "=" * 60)
        print("✓ Matplotlib演示完成!")
        print("=" * 60)
        
        print("\n生成的文件:")
        print(f"  - 演示图片: images/matplotlib_demo/")
        print(f"  - 3D模型: {obj_path}")
        print(f"  - 模型图像: output/matplotlib_3d_model.png")
        print(f"  - 纹理预览: output/texture_preview.png")
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

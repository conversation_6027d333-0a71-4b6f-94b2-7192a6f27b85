"""
完整的3D可视化演示
不依赖Open3D，使用matplotlib进行3D显示
"""

import sys
import os
import numpy as np
import cv2
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from image_processor import ImageProcessor
    from geometry_builder_simple import SimpleGeometryBuilder
    print("✓ 成功导入所有模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)


class Complete3DVisualizer:
    """完整的3D可视化器"""
    
    def __init__(self, dimensions=(2.0, 2.0, 2.0)):
        """初始化可视化器"""
        self.dimensions = dimensions
        self.image_processor = ImageProcessor()
        self.geometry_builder = SimpleGeometryBuilder(dimensions)
        self.processed_images = None
        self.vertices = None
        self.faces = None
    
    def load_and_process_images(self, image_paths, enhance=False):
        """加载和处理图片"""
        print("加载和处理图片...")
        self.processed_images = self.image_processor.process_cube_images(
            image_paths, enhance=enhance)
        
        if self.processed_images:
            print(f"  ✓ 成功处理 {len(self.processed_images)} 张图片")
            return True
        else:
            print("  ✗ 图片处理失败")
            return False
    
    def create_geometry(self):
        """创建几何体"""
        print("创建几何体...")
        self.vertices = self.geometry_builder.create_box_vertices()
        self.faces = self.geometry_builder.create_box_faces()
        print(f"  ✓ 创建了 {len(self.vertices)} 个顶点和 {len(self.faces)} 个面")
        return True
    
    def create_cube_faces_for_display(self):
        """为显示创建立方体面"""
        if self.vertices is None:
            return []
        
        # 定义6个面，每个面4个顶点
        faces = [
            [self.vertices[0], self.vertices[1], self.vertices[2], self.vertices[3]],  # 前面
            [self.vertices[4], self.vertices[7], self.vertices[6], self.vertices[5]],  # 后面
            [self.vertices[0], self.vertices[4], self.vertices[7], self.vertices[3]],  # 左面
            [self.vertices[1], self.vertices[2], self.vertices[6], self.vertices[5]],  # 右面
            [self.vertices[0], self.vertices[1], self.vertices[5], self.vertices[4]],  # 底面
            [self.vertices[3], self.vertices[2], self.vertices[6], self.vertices[7]],  # 顶面
        ]
        
        return faces
    
    def get_face_colors(self):
        """从图片计算面的颜色"""
        if not self.processed_images:
            # 默认颜色
            return [
                (1.0, 0.4, 0.4, 0.8),  # 前面 - 红色
                (0.4, 1.0, 0.4, 0.8),  # 后面 - 绿色
                (0.4, 0.4, 1.0, 0.8),  # 左面 - 蓝色
                (1.0, 1.0, 0.4, 0.8),  # 右面 - 黄色
                (1.0, 0.4, 1.0, 0.8),  # 底面 - 紫色
                (0.4, 1.0, 1.0, 0.8),  # 顶面 - 青色
            ]
        
        face_names = ['front', 'back', 'left', 'right', 'bottom', 'top']
        colors = []
        
        for face_name in face_names:
            if face_name in self.processed_images:
                # 计算图片的平均颜色
                image = self.processed_images[face_name]
                mean_color = np.mean(image.reshape(-1, 3), axis=0) / 255.0
                colors.append((*mean_color, 0.8))  # 添加透明度
            else:
                # 默认颜色
                colors.append((0.7, 0.7, 0.7, 0.8))
        
        return colors
    
    def display_texture_preview(self):
        """显示纹理预览"""
        if not self.processed_images:
            return
        
        print("显示纹理预览...")
        
        # 创建子图
        num_images = len(self.processed_images)
        cols = min(3, num_images)
        rows = (num_images + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(cols * 3, rows * 3))
        if num_images == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.reshape(1, -1)
        
        # 显示每张图片
        for i, (face_name, image) in enumerate(self.processed_images.items()):
            row = i // cols
            col = i % cols
            
            if rows > 1:
                ax = axes[row, col]
            else:
                ax = axes[col]
            
            ax.imshow(image)
            ax.set_title(f'{face_name.upper()}面', fontsize=12, fontweight='bold')
            ax.axis('off')
        
        # 隐藏多余的子图
        for i in range(num_images, rows * cols):
            row = i // cols
            col = i % cols
            if rows > 1:
                axes[row, col].axis('off')
            else:
                axes[col].axis('off')
        
        plt.tight_layout()
        plt.suptitle('纹理预览', fontsize=16, fontweight='bold', y=1.02)
        
        # 保存
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        plt.savefig(output_dir / "texture_preview.png", dpi=300, bbox_inches='tight')
        print(f"  ✓ 纹理预览已保存: output/texture_preview.png")
        
        plt.show()
    
    def display_3d_model(self, show_wireframe=True):
        """显示3D模型"""
        if self.vertices is None:
            print("  ✗ 没有几何体数据")
            return
        
        print("显示3D模型...")
        
        # 创建图形
        fig = plt.figure(figsize=(15, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # 创建面
        faces = self.create_cube_faces_for_display()
        colors = self.get_face_colors()
        
        # 绘制面
        face_collection = Poly3DCollection(faces, facecolors=colors, alpha=0.8)
        ax.add_collection3d(face_collection)
        
        # 绘制线框
        if show_wireframe:
            edges = self.geometry_builder.get_wireframe_edges()
            for edge in edges:
                points = self.vertices[list(edge)]
                ax.plot3D(*points.T, 'k-', alpha=0.6, linewidth=1)
        
        # 设置坐标轴
        ax.set_xlabel('X轴', fontsize=12)
        ax.set_ylabel('Y轴', fontsize=12)
        ax.set_zlabel('Z轴', fontsize=12)
        
        # 设置相等的坐标轴比例
        max_range = np.array([
            self.vertices[:, 0].max() - self.vertices[:, 0].min(),
            self.vertices[:, 1].max() - self.vertices[:, 1].min(),
            self.vertices[:, 2].max() - self.vertices[:, 2].min()
        ]).max() / 2.0
        
        mid_x = (self.vertices[:, 0].max() + self.vertices[:, 0].min()) * 0.5
        mid_y = (self.vertices[:, 1].max() + self.vertices[:, 1].min()) * 0.5
        mid_z = (self.vertices[:, 2].max() + self.vertices[:, 2].min()) * 0.5
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        
        # 设置标题
        ax.set_title('3D长方体模型', fontsize=16, fontweight='bold')
        
        # 添加控制说明
        control_text = """
        控制说明:
        鼠标拖拽: 旋转模型
        滚轮: 缩放
        """
        
        fig.text(0.02, 0.98, control_text, transform=fig.transFigure,
                fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 保存
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        plt.savefig(output_dir / "3d_model.png", dpi=300, bbox_inches='tight')
        print(f"  ✓ 3D模型图像已保存: output/3d_model.png")
        
        plt.show()
    
    def save_model(self, filename):
        """保存模型"""
        if self.geometry_builder:
            return self.geometry_builder.save_obj_file(filename)
        return False
    
    def create_texture_atlas(self):
        """创建纹理图集"""
        if not self.processed_images:
            return None
        
        print("创建纹理图集...")
        
        # 获取图片尺寸
        sample_image = next(iter(self.processed_images.values()))
        img_height, img_width = sample_image.shape[:2]
        
        # 创建2x3的图集布局
        atlas_width = img_width * 3
        atlas_height = img_height * 2
        atlas = np.zeros((atlas_height, atlas_width, 3), dtype=np.uint8)
        
        # 布局位置
        positions = {
            'left': (0, 0),
            'front': (1, 0),
            'right': (2, 0),
            'back': (0, 1),
            'top': (1, 1),
        }
        
        # 放置图片
        for face_name, image in self.processed_images.items():
            if face_name in positions:
                col, row = positions[face_name]
                start_x = col * img_width
                start_y = row * img_height
                end_x = start_x + img_width
                end_y = start_y + img_height
                
                atlas[start_y:end_y, start_x:end_x] = image
                print(f"  ✓ 放置 {face_name} 面到图集位置 ({col}, {row})")
        
        # 保存图集
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        atlas_path = output_dir / "texture_atlas.png"
        atlas_bgr = cv2.cvtColor(atlas, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(atlas_path), atlas_bgr)
        print(f"  ✓ 纹理图集已保存: {atlas_path}")
        
        return atlas


def create_demo_images():
    """创建演示图片"""
    print("创建演示图片...")
    
    # 创建images目录
    images_dir = Path("images/complete_demo")
    images_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义每个面的颜色和图案
    face_configs = {
        'front': {'color': [255, 100, 100], 'text': 'FRONT'},   # 红色
        'back': {'color': [100, 255, 100], 'text': 'BACK'},     # 绿色
        'left': {'color': [100, 100, 255], 'text': 'LEFT'},     # 蓝色
        'right': {'color': [255, 255, 100], 'text': 'RIGHT'},   # 黄色
        'top': {'color': [255, 100, 255], 'text': 'TOP'},       # 紫色
    }
    
    image_paths = {}
    
    for face, config in face_configs.items():
        # 创建512x512的图片
        image = np.full((512, 512, 3), config['color'], dtype=np.uint8)
        
        # 添加渐变效果
        for i in range(512):
            for j in range(512):
                factor = 0.8 + 0.4 * (i + j) / 1024
                image[i, j] = (image[i, j] * factor).astype(np.uint8)
        
        # 添加文字标识
        cv2.putText(image, config['text'], (120, 256), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 4)
        
        # 添加装饰边框
        cv2.rectangle(image, (20, 20), (491, 491), (255, 255, 255), 8)
        cv2.rectangle(image, (40, 40), (471, 471), (0, 0, 0), 4)
        
        # 保存图片
        image_path = images_dir / f"{face}.png"
        cv2.imwrite(str(image_path), image)
        image_paths[face] = str(image_path)
        print(f"  ✓ 创建 {face} 面图片: {image_path}")
    
    return image_paths


def main():
    """主函数"""
    print("=" * 60)
    print("完整3D长方体可视化系统演示")
    print("=" * 60)
    
    try:
        # 创建演示图片
        image_paths = create_demo_images()
        
        # 创建可视化器
        visualizer = Complete3DVisualizer(dimensions=(3.0, 2.0, 1.5))
        
        # 加载和处理图片
        if not visualizer.load_and_process_images(image_paths, enhance=True):
            return
        
        # 创建几何体
        if not visualizer.create_geometry():
            return
        
        # 创建纹理图集
        atlas = visualizer.create_texture_atlas()
        
        # 保存3D模型
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        obj_path = output_dir / "complete_demo_cube.obj"
        
        if visualizer.save_model(str(obj_path)):
            print(f"✓ 3D模型已保存: {obj_path}")
        
        # 显示纹理预览
        visualizer.display_texture_preview()
        
        # 显示3D模型
        visualizer.display_3d_model(show_wireframe=True)
        
        print("\n" + "=" * 60)
        print("✓ 完整演示成功!")
        print("=" * 60)
        
        print("\n生成的文件:")
        print(f"  - 演示图片: images/complete_demo/")
        print(f"  - 3D模型: {obj_path}")
        print(f"  - 纹理图集: output/texture_atlas.png")
        print(f"  - 3D模型图像: output/3d_model.png")
        print(f"  - 纹理预览: output/texture_preview.png")
        
        print("\n功能验证:")
        print("  ✓ 图像处理和加载")
        print("  ✓ 几何体创建")
        print("  ✓ 纹理映射")
        print("  ✓ 3D可视化")
        print("  ✓ 模型导出")
        
    except Exception as e:
        print(f"✗ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

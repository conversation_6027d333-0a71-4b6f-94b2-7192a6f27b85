"""
JSON文件处理器 - 用于LabVIEW JSON通信
读取配置JSON文件，处理3D可视化，写入结果JSON文件
"""

import sys
import json
import time
import traceback
from pathlib import Path

# 添加src目录到路径
sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from image_processor import ImageProcessor
    from geometry_builder_simple import SimpleGeometryBuilder
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class JSONProcessor:
    """JSON文件处理器"""
    
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.geometry_builder = None
        
    def process_config_file(self, config_path: str) -> dict:
        """
        处理配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            处理结果字典
        """
        start_time = time.time()
        
        try:
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"读取配置文件: {config_path}")
            
            # 验证配置
            if not self._validate_config(config):
                return self._create_error_result("配置文件格式错误")
            
            # 处理图片
            processed_images = self.image_processor.process_cube_images(
                config["input_images"],
                enhance=config.get("processing_options", {}).get("enhance", False)
            )
            
            if not processed_images:
                return self._create_error_result("图片处理失败")
            
            print(f"成功处理 {len(processed_images)} 张图片")
            
            # 创建几何体
            dimensions = config.get("dimensions", {"width": 2.0, "height": 2.0, "depth": 2.0})
            self.geometry_builder = SimpleGeometryBuilder(
                dimensions=(dimensions["width"], dimensions["height"], dimensions["depth"])
            )
            
            vertices = self.geometry_builder.create_box_vertices()
            faces = self.geometry_builder.create_box_faces()
            
            print(f"创建几何体: {len(vertices)} 个顶点, {len(faces)} 个面")
            
            # 保存模型
            output_options = config.get("output_options", {})
            model_path = output_options.get("model_path", "output.obj")
            
            # 确保输出目录存在
            output_dir = Path(model_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            
            success = self.geometry_builder.save_obj_file(model_path)
            
            if not success:
                return self._create_error_result("模型保存失败")
            
            print(f"模型已保存: {model_path}")
            
            # 创建模型信息文件
            info_path = str(Path(model_path).with_suffix('.json'))
            model_info = self.geometry_builder.get_model_info()
            model_info["processed_images"] = list(processed_images.keys())
            
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(model_info, f, indent=2, ensure_ascii=False)
            
            # 创建成功结果
            processing_time = time.time() - start_time
            
            result = {
                "success": True,
                "output_files": {
                    "model": model_path,
                    "info": info_path
                },
                "model_info": {
                    "num_vertices": len(vertices),
                    "num_triangles": len(faces),
                    "dimensions": dimensions,
                    "processed_images": list(processed_images.keys())
                },
                "processing_time": round(processing_time, 2),
                "error_message": None,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            print(f"处理完成，耗时: {processing_time:.2f} 秒")
            return result
            
        except Exception as e:
            error_msg = f"处理过程中发生错误: {str(e)}"
            print(error_msg)
            traceback.print_exc()
            return self._create_error_result(error_msg)
    
    def _validate_config(self, config: dict) -> bool:
        """验证配置文件格式"""
        required_keys = ["input_images"]
        
        for key in required_keys:
            if key not in config:
                print(f"配置文件缺少必需的键: {key}")
                return False
        
        # 检查图片路径
        input_images = config["input_images"]
        required_faces = ["front", "back", "left", "right", "top"]
        
        for face in required_faces:
            if face not in input_images:
                print(f"缺少 {face} 面的图片路径")
                return False
            
            if not Path(input_images[face]).exists():
                print(f"{face} 面图片文件不存在: {input_images[face]}")
                return False
        
        return True
    
    def _create_error_result(self, error_message: str) -> dict:
        """创建错误结果"""
        return {
            "success": False,
            "output_files": None,
            "model_info": None,
            "processing_time": 0,
            "error_message": error_message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python json_processor.py <config_file.json>")
        print("示例: python json_processor.py config.json")
        sys.exit(1)
    
    config_path = sys.argv[1]
    
    if not Path(config_path).exists():
        print(f"配置文件不存在: {config_path}")
        sys.exit(1)
    
    # 处理配置文件
    processor = JSONProcessor()
    result = processor.process_config_file(config_path)
    
    # 写入结果文件
    result_path = str(Path(config_path).with_name("result.json"))
    
    try:
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"结果已保存到: {result_path}")
        
        # 返回适当的退出码
        if result["success"]:
            print("处理成功完成!")
            sys.exit(0)
        else:
            print(f"处理失败: {result['error_message']}")
            sys.exit(1)
            
    except Exception as e:
        print(f"保存结果文件失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
